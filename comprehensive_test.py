#!/usr/bin/env python3
"""
Comprehensive Table Processing Test Script with File Output

This script tests the table processing functionality on all available files:
- di_parser_output.txt (original dataset)
- All .txt files in the test/ folder (automatically discovered)

SCOPE: Comprehensive testing with all available files
FUNCTION TESTED: correct_tables() from finalcode2.py
OUTPUT: File output saved to 'comprehensive_test_output.txt'

This provides comprehensive testing coverage for all table conversion functionality
across the complete test suite. Used for full validation of system capabilities
with persistent file output for analysis and verification.

Key Features Tested:
- All table structure types and patterns
- Complex header spanning (colspan/rowspan)
- Hierarchical and nested table structures
- Alignment-based tables with empty cells
- Grouped and sectioned data tables
- Symbol replacement and context awareness
- Column suffix combining with comma separation
- Caption preservation across all formats
- Error handling for malformed tables
- Fallback parsing strategies
- Individual table processing workflow
- Memory-efficient processing without temporary files

Output Format:
- All converted tables saved to single file
- Organized by source file for easy analysis
- Complete JSON conversion results preserved
- Processing statistics and summary included
"""

import finalcode2
import os

print('Testing all files...')
print()

# Open output file for writing all results
with open('comprehensive_test_output.txt', 'w', encoding='utf-8') as output_file:
    output_file.write('=== COMPREHENSIVE TABLE CONVERSION OUTPUTS ===\n\n')

    # Get all files in test folder automatically
    trial_files = []
    if os.path.exists('test'):
        for filename in os.listdir('test'):
            if filename.endswith('.txt'):
                trial_files.append(f'test/{filename}')

    # Sort files numerically (trial1, trial2, trial3... not trial1, trial10, trial11...)
    def natural_sort_key(filename):
        import re
        # Extract numbers from filename for proper numerical sorting
        numbers = re.findall(r'\d+', filename)
        if numbers:
            return (filename.split('trial')[0], int(numbers[0]) if numbers else 0, filename)
        return (filename, 0, filename)

    trial_files.sort(key=natural_sort_key)

    print(f'Found {len(trial_files)} files in test folder')
    print()

    total_tables = 0
    total_json = 0
    total_html = 0
    files_with_empty_preservation = 0

    # Process di_parser_output.txt first if it exists
    if os.path.exists('di_parser_output.txt'):
        try:
            print('Processing di_parser_output.txt...')
            content = open('di_parser_output.txt', 'r', encoding='utf-8').read()
            result = finalcode2.correct_tables(content)

            # Write to output file
            output_file.write('=== DI_PARSER_OUTPUT.TXT ===\n')
            output_file.write(result)
            output_file.write('\n\n')

            # Count tables for statistics
            tables = []
            start = 0
            while True:
                table_start = result.find('<table>', start)
                if table_start == -1:
                    break
                table_end = result.find('</table>', table_start) + 8
                tables.append(result[table_start:table_end])
                start = table_end

            json_count = 0
            html_count = 0

            for table in tables:
                # Check for JSON patterns: arrays with objects, single objects, or empty arrays
                if (('[{' in table and '}]' in table) or
                    ('{' in table and '}' in table and not '<tr>' in table) or
                    ('[]' in table and not '<tr>' in table)):
                    json_count += 1
                else:
                    html_count += 1

            total_tables += len(tables)
            total_json += json_count
            total_html += html_count

            success_rate = 100 * json_count / len(tables) if tables else 0
            empty_preserved = '""' in result or '": ""' in result
            if empty_preserved:
                files_with_empty_preservation += 1

            status = '✅' if json_count == len(tables) else '❌'
            print(f'{status} di_parser_output.txt: {len(tables)} tables, {json_count} JSON ({success_rate:.1f}%)')

        except Exception as e:
            print(f'❌ Error processing di_parser_output.txt: {e}')
            output_file.write(f'=== DI_PARSER_OUTPUT.TXT ===\nError: {e}\n\n')

    # Process all trial files
    for trial_file in trial_files:
        try:
            print(f'Processing {trial_file}...')
            content = open(trial_file, 'r', encoding='utf-8').read()
            result = finalcode2.correct_tables(content)

            # Write to output file
            output_file.write(f'=== {trial_file.upper()} ===\n')
            output_file.write(result)
            output_file.write('\n\n')

            # Count tables for statistics
            tables = []
            start = 0
            while True:
                table_start = result.find('<table>', start)
                if table_start == -1:
                    break
                table_end = result.find('</table>', table_start) + 8
                tables.append(result[table_start:table_end])
                start = table_end

            json_count = 0
            html_count = 0

            for table in tables:
                # Check for JSON patterns: arrays with objects, single objects, or empty arrays
                if (('[{' in table and '}]' in table) or
                    ('{' in table and '}' in table and not '<tr>' in table) or
                    ('[]' in table and not '<tr>' in table)):
                    json_count += 1
                else:
                    html_count += 1

            total_tables += len(tables)
            total_json += json_count
            total_html += html_count

            success_rate = 100 * json_count / len(tables) if tables else 0

            # Check if empty values are preserved
            empty_preserved = '""' in result or '": ""' in result
            if empty_preserved:
                files_with_empty_preservation += 1

            status = '✅' if json_count == len(tables) else '❌'
            print(f'{status} {trial_file}: {len(tables)} tables, {json_count} JSON ({success_rate:.1f}%)')

        except Exception as e:
            print(f'❌ Error processing {trial_file}: {e}')
            output_file.write(f'=== {trial_file.upper()} ===\nError: {e}\n\n')

print()
print('🎯 COMPREHENSIVE RESULTS:')
total_files = len(trial_files) + (1 if os.path.exists('di_parser_output.txt') else 0)
print(f'   📊 Total files tested: {total_files}')
print(f'   📊 Total tables processed: {total_tables}')
print(f'   ✅ Successfully converted to JSON: {total_json} ({100*total_json/total_tables:.1f}%)')
print(f'   ❌ Still in HTML format: {total_html}')
print()

if total_json == total_tables:
    print('✅ ALL TABLES CONVERTED SUCCESSFULLY')
else:
    print('❌ Some tables failed to convert')

print()
print('🏆 FINAL RESULTS:')
print(f'   📊 Grand total tables: {total_tables}')
print(f'   ✅ JSON converted: {total_json} ({100*total_json/total_tables:.1f}%)')
print(f'   ❌ HTML format: {total_html}')
print()
print(f'📄 All outputs saved to: comprehensive_test_output.txt')
