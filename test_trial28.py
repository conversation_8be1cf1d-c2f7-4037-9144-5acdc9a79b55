#!/usr/bin/env python3
"""
Test the new trial28.txt file with tables having 1-7 header rows.
"""

import finalcode2

def test_trial28():
    """Test trial28.txt with 7 tables having 1-7 header rows"""
    
    with open('test/trial28.txt', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("Testing trial28.txt with tables having 1-7 header rows...")
    result = finalcode2.correct_tables(content)
    
    # Count tables and analyze results
    table_count = result.count('<table>')
    title_count = result.count('<title>')
    
    print(f"Tables processed: {table_count}")
    print(f"Title tags created: {title_count}")
    
    # Extract individual tables for analysis
    tables = []
    start = 0
    for i in range(table_count):
        table_start = result.find('<table>', start)
        if table_start == -1:
            break
        table_end = result.find('</table>', table_start) + 8
        tables.append(result[table_start:table_end])
        start = table_end
    
    print(f"\nAnalysis of each table:")
    for i, table in enumerate(tables, 1):
        has_title = '<title>' in table
        has_caption = '<caption>' in table
        
        # Extract caption for identification
        caption = ""
        if has_caption:
            cap_start = table.find('<caption>') + 9
            cap_end = table.find('</caption>')
            caption = table[cap_start:cap_end] if cap_start > 8 and cap_end > cap_start else ""
        
        print(f"Table {i}: {caption}")
        print(f"  - Has title tag: {'✅' if has_title else '❌'}")
        
        if has_title:
            title_start = table.find('<title>') + 7
            title_end = table.find('</title>')
            title = table[title_start:title_end] if title_start > 6 and title_end > title_start else ""
            print(f"  - Title content: '{title}'")
        
        print()

if __name__ == "__main__":
    test_trial28()
