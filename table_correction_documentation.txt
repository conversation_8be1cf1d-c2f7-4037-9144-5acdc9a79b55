# TABLE CORRECTION SYSTEM DOCUMENTATION

## OVERVIEW
The Table Correction System (finalcode2.py) is a comprehensive Python tool that processes HTML tables and converts them to structured JSON format. The system uses in-memory processing for optimal performance and supports various table structures including hierarchical, wide-column grouped, and sparse vertical layouts.

## KEY FEATURES
- In-memory table processing (no temporary files)
- Multiple table structure classification
- Unicode symbol detection and replacement
- Caption preservation with <caption> tags
- Hierarchical JSON conversion strategies
- Single-line output formatting
- Comprehensive error handling

## CORE FUNCTIONS

### TABLE PROCESSING FUNCTIONS

**correct_tables(input_string: str) -> dict**
- Main processing function that orchestrates the entire table correction workflow
- Takes string input containing HTML tables
- Returns processed result with corrected tables in JSON format
- Handles nested dictionary input formats
- Processes tables individually in sequential order

**INPUT FORMAT REQUIREMENTS:**
- **Line 19**: Main entry point for processing - call `correct_tables(your_string_here)`
- Accepts plain string containing HTML tables
- Accepts nested dictionary format: `{'content': 'HTML_content', 'language': 'en'}`
- Input string can contain mixed content (text + HTML tables)
- Tables must be in valid HTML format with `<table>`, `<tr>`, `<td>`, `<th>` tags

**INTEGRATION WITH OTHER STRING PROCESSING:**
- Can be chained with other text processing functions
- Preserves all non-table content unchanged
- Output maintains original string structure with tables converted to JSON
- Compatible with file reading functions: `correct_tables(open('file.txt').read())`
- Works with web scraping results and HTML parsing outputs

**process_tables(html_content: str) -> List[Dict]**
- Extracts and parses HTML tables from content
- Uses lxml as primary parser with BeautifulSoup fallback
- Returns list of table dictionaries with structure information
- Handles malformed HTML gracefully

**process_tables_with_lxml(html_content: str) -> List[Dict]**
- Primary table extraction using lxml parser
- Faster performance for well-formed HTML
- Extracts table structure including rowspan/colspan information

**process_tables_with_bs4(html_content: str) -> List[Dict]**
- Fallback table extraction using BeautifulSoup
- Better handling of malformed HTML structures
- Used when lxml parsing fails

### TABLE CLASSIFICATION FUNCTIONS

**classify_table_from_data(table_data: Dict) -> str**
- Direct table classification without file I/O operations
- Analyzes table structure to determine conversion strategy
- Returns classification string for appropriate processing

**classify_table_structure(table_data: Dict, filename: str = "") -> str**
- Comprehensive table structure analysis
- Identifies patterns like hierarchical, wide-column grouped, sparse vertical layouts
- Uses structural analysis rather than content-specific rules

**build_expanded_matrix(table_data: Dict) -> List[List[str]]**
- Constructs full table matrix handling rowspan/colspan
- Expands merged cells to create complete grid structure
- Essential for accurate table analysis

### CONVERSION FUNCTIONS

**convert_to_key_value_json(table_data: Dict, classification: str) -> Union[Dict, List]**
- Main conversion function that applies appropriate strategy based on classification
- Supports multiple output formats: hierarchical, grouped, wide-column grouped
- Preserves table structure semantics

**convert_hierarchical_nested(matrix: List[List[str]]) -> Dict**
- Converts tables with nested hierarchical structure
- Creates multi-level dictionary representation
- Handles complex parent-child relationships

**convert_temporal_grouped(matrix: List[List[str]]) -> Dict**
- Processes wide-column grouped data tables
- Organizes data by structural groupings with wide column spans
- Maintains logical ordering and hierarchical relationships

**convert_simple_structured(matrix: List[List[str]]) -> List[Dict]**
- Handles simple row-column tables
- Creates list of dictionaries with consistent keys
- Best for straightforward tabular data

### SYMBOL PROCESSING FUNCTIONS

**scan_matrix_for_symbols(matrix: List[List[str]]) -> List[Dict]**
- Detects Unicode symbols in table content
- Categorizes symbols (checkbox, checkmark, cross, neutral, other)
- Excludes currency symbols from processing

**process_json_data_with_symbol_replacement(data, context_map: Dict) -> Union[Dict, List]**
- Replaces Unicode symbols with semantic text
- Uses context-aware replacement for ambiguous symbols
- Supports both direct and contextual replacement strategies

**analyze_json_structure_for_context(data) -> Dict**
- Analyzes table structure to determine symbol context
- Creates mapping for context-aware symbol replacement
- Handles complex nested structures

## SYMBOL MAPPINGS REFERENCE

**UNICODE SYMBOL DEFINITIONS (Lines 27-33 in finalcode2.py):**
- **Checkbox Symbols**: ☐ (U+2610), ☑ (U+2611), ☒ (U+2612)
- **Checkmark Symbols**: ✓, ✔, ✅, 🗸 (Various checkmark symbols)
- **Cross Symbols**: ✗, ✘, ❌, ✕, ❎ (Various cross/X symbols)
- **Neutral Symbols**: ◻, ⬜, ▪, —, ❓ (Neutral or unknown state)

**CONTEXT-AWARE MAPPINGS (Lines 40-103 in finalcode2.py):**
- **Boolean Context**: Yes/No/Maybe
- **Completion Context**: Completed/Incomplete/In progress
- **Availability Context**: Available/Unavailable/Unknown
- **Participation Context**: Participating/Not participating/Undecided
- **Correctness Context**: Correct/Incorrect/Unknown

**FALLBACK MAPPINGS (Lines 105-123 in finalcode2.py):**
- **Positive Symbols**: ☑→Yes, ☒→Yes, ✓→Yes, ✔→Yes, ✅→Yes, 🗸→Yes
- **Negative Symbols**: ☐→No, ✗→No, ✘→No, ❌→No, ✕→No, ❎→No
- **Neutral Symbols**: ◻→Unknown, ⬜→Unknown, —→N/A, ❓→Unknown

### UTILITY FUNCTIONS

**remove_unnecessary_brackets(data) -> Union[Dict, List]**
- Cleans up JSON structure by removing redundant nesting
- Simplifies output format for better readability
- Preserves essential data relationships

**remove_whitespace_and_blank_lines(text: str) -> str**
- Removes all blank lines and excess whitespace
- Creates clean single-line output format
- Essential for consistent formatting

**extract_table_with_span_lxml(table_el) -> Dict**
- Extracts individual table with span information using lxml
- Handles rowspan and colspan attributes
- Returns structured table data

**extract_table_with_span_bs4(table_el) -> Dict**
- Extracts individual table with span information using BeautifulSoup
- Fallback method for complex HTML structures
- Maintains compatibility with various HTML formats

## USAGE WITH OTHER STRING PROCESSING FUNCTIONS

**COMMON INTEGRATION PATTERNS:**

```python
# 1. File Processing Pipeline
def process_html_file(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()

    # Apply table correction
    result = correct_tables(content)

    # Further string processing
    processed_content = result.get('content', content)
    return processed_content

# 2. Web Scraping Integration
import requests
from bs4 import BeautifulSoup

def scrape_and_process_tables(url):
    response = requests.get(url)
    html_content = response.text

    # Extract tables and convert to JSON
    result = correct_tables(html_content)
    return result

# 3. Batch Processing
def process_multiple_files(file_list):
    results = []
    for filename in file_list:
        content = open(filename).read()
        processed = correct_tables(content)
        results.append(processed)
    return results

# 4. Chain with Text Cleaning
def comprehensive_text_processing(raw_html):
    # Step 1: Table correction
    table_result = correct_tables(raw_html)

    # Step 2: Additional text processing
    clean_text = table_result['content']
    clean_text = clean_text.replace('\n\n', '\n')  # Remove extra newlines
    clean_text = re.sub(r'\s+', ' ', clean_text)   # Normalize whitespace

    return clean_text
```

## PROGRAM FLOW

### STEP 1: INPUT PROCESSING
1. **Input Validation**: Check if input is string or nested dictionary format
2. **Format Handling**: Extract content from nested structures if present
3. **Content Preparation**: Prepare HTML content for table extraction

### STEP 2: TABLE EXTRACTION
1. **HTML Parsing**: Use lxml parser (primary) or BeautifulSoup (fallback)
2. **Table Detection**: Identify all <table> elements in content
3. **Structure Analysis**: Extract table structure including spans and positions
4. **Data Extraction**: Convert HTML table elements to structured dictionaries

### STEP 3: INDIVIDUAL TABLE PROCESSING
For each table found:
1. **Position Tracking**: Record start/end positions in original content
2. **Matrix Building**: Create expanded matrix handling rowspan/colspan
3. **Classification**: Determine table structure type for conversion strategy
4. **Conversion**: Apply appropriate JSON conversion based on classification
5. **Symbol Processing**: Detect and replace Unicode symbols if present
6. **Content Replacement**: Replace original table with JSON in content string

### STEP 4: SYMBOL ANALYSIS AND REPLACEMENT
1. **Symbol Detection**: Scan table matrix for Unicode symbols
2. **Categorization**: Classify symbols by type (checkbox, checkmark, etc.)
3. **Context Analysis**: Determine semantic context for ambiguous symbols
4. **Replacement**: Replace symbols with appropriate text (Yes/No/Correct/etc.)

### STEP 5: OUTPUT FORMATTING
1. **Caption Handling**: Preserve table captions in <caption> tags
2. **JSON Formatting**: Create clean JSON with proper formatting
3. **Tag Wrapping**: Wrap each table in <table></table> tags
4. **Whitespace Cleanup**: Remove unnecessary whitespace for single-line output

### STEP 6: FINAL PROCESSING
1. **Content Assembly**: Combine all processed tables back into original content
2. **Format Wrapping**: Apply final format wrapper if required
3. **Result Return**: Return processed content with corrected tables

## SUPPORTED TABLE TYPES
- **Hierarchical Nested**: Multi-level parent-child relationships
- **Wide Column Grouped**: Tables with wide column spans and grouped data organization
- **Simple Structured**: Basic row-column format
- **Sectioned Multicolumn**: Complex multi-section layouts
- **Header Heavy**: Tables with extensive header structures
- **Data Heavy**: Tables with minimal headers, extensive data

## OUTPUT FORMAT
- Tables wrapped in <table></table> tags
- Captions preserved in <caption></caption> tags
- JSON data in compact single-line format
- Unicode symbols replaced with semantic text
- Original non-table content preserved unchanged

## PERFORMANCE CHARACTERISTICS
- In-memory processing (no temporary files)
- Sequential table processing for accuracy
- Efficient parser selection (lxml primary, BeautifulSoup fallback)
- Minimal memory footprint
- Fast execution for large documents

## ERROR HANDLING
- Graceful fallback from lxml to BeautifulSoup parsing
- Skip invalid tables without stopping processing
- Continue processing on individual table errors
- Preserve original content when conversion fails
- Comprehensive exception handling throughout pipeline

---

## DOCUMENTATION NOTES

**⚠️ DOCX FILE LIMITATION:**
This documentation is also available in `table_correction_documentation.docx` format. However, the .docx file cannot be automatically updated using the available tools and may require manual synchronization with changes made to this .txt version. Always refer to this .txt file for the most current information.

**📍 KEY REFERENCE LINES:**
- **Line 19**: Main function entry point `correct_tables()`
- **Lines 27-33**: Unicode symbol definitions in finalcode2.py
- **Lines 40-103**: Context-aware mapping patterns in finalcode2.py
- **Lines 105-123**: Fallback symbol mappings in finalcode2.py
- **Lines 154-205**: Integration examples and usage patterns
