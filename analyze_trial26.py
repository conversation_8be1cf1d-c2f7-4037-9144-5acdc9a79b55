#!/usr/bin/env python3
import finalcode2
import json
import re

# Load trial26.txt
with open('test/trial26.txt', 'r', encoding='utf-8') as f:
    content = f.read()

data = json.loads(content)
html_content = data['content']

# Extract all tables
tables = re.findall(r'<table>.*?</table>', html_content, re.DOTALL)

print('=== ANALYZING ALL TABLES IN TRIAL26.TXT ===')
print()

for i, table_html in enumerate(tables, 1):
    print(f'--- TABLE {i} ---')
    
    # Process table
    processed_tables = finalcode2.process_tables(table_html)
    if processed_tables:
        table = processed_tables[0]
        
        # Get features and classification
        features = finalcode2.extract_table_features(table)
        classification = finalcode2.classify_table_from_data(table)
        
        print(f'Classification: {classification}')
        print(f'Total rows: {features["total_rows"]}')
        print(f'Header ratio: {features["header_ratio"]:.3f}')
        print(f'Colspan cells: {features["colspan_cells"]}')
        print(f'Rowspan cells: {features["rowspan_cells"]}')
        print(f'Empty ratio: {features["empty_ratio"]:.3f}')
        
        # Check multiple_header_rows criteria
        meets_criteria = (features['total_rows'] >= 4 and
                         features['header_ratio'] > 0.4 and
                         features['colspan_cells'] == 0 and features['rowspan_cells'] == 0 and
                         features['empty_ratio'] < 0.1)
        print(f'Meets multiple_header_rows criteria: {meets_criteria}')
        
        # Show raw structure
        matrix = finalcode2.build_expanded_matrix(table)
        print('Matrix:')
        for j, row in enumerate(matrix):
            print(f'  Row {j}: {row}')
        print()
