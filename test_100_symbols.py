#!/usr/bin/env python3
"""
Comprehensive test with 100 different types of symbols in UTF-8 and UTF-16 encodings.
"""

import finalcode2
import tempfile
import os
import json

# Create 100 different Unicode symbols using safe Unicode code points
def create_symbol_test_set():
    """Create test set with 100 Unicode symbols using chr() for safety"""

    symbol_codes = [
        # Checkmarks and crosses
        (0x2713, 'Yes'), (0x2714, 'Yes'), (0x2705, 'Yes'), (0x2611, 'Yes'), (0x2612, 'Yes'),
        (0x2717, 'No'), (0x2718, 'No'), (0x274C, 'No'), (0x274E, 'No'), (0x2610, 'No'),
        (0x26A0, 'Warning'), (0x26D4, 'No'), (0x1F6AB, 'No'), (0x2B55, 'No'), (0x2757, 'Warning'),
        (0x2753, 'Unknown'), (0x2754, 'Unknown'), (0x203C, 'Warning'), (0x2049, 'Unknown'),

        # Geometric shapes
        (0x25CF, 'Yes'), (0x25CB, 'No'), (0x25C9, 'Yes'), (0x25EF, 'No'), (0x25D0, 'Partial'),
        (0x25A0, 'Yes'), (0x25A1, 'No'), (0x25AA, 'Yes'), (0x25AB, 'No'), (0x25FC, 'Yes'),
        (0x25FB, 'No'), (0x25B2, 'Up'), (0x25B3, 'Up'), (0x25BC, 'Down'), (0x25BD, 'Down'),
        (0x25C6, 'Yes'), (0x25C7, 'No'), (0x25C8, 'Yes'), (0x2B1B, 'Yes'), (0x2B1C, 'No'),

        # Arrows and directions
        (0x2192, 'Right'), (0x2190, 'Left'), (0x2191, 'Up'), (0x2193, 'Down'), (0x2194, 'Both'),
        (0x21D2, 'Right'), (0x21D0, 'Left'), (0x21D1, 'Up'), (0x21D3, 'Down'), (0x21D4, 'Both'),
        (0x27A1, 'Right'), (0x2B05, 'Left'), (0x2B06, 'Up'), (0x2B07, 'Down'), (0x21A9, 'Return'),
        (0x21AA, 'Return'), (0x2934, 'Up'), (0x2935, 'Down'), (0x1F504, 'Refresh'), (0x1F503, 'Refresh'),

        # Mathematical symbols
        (0x00B1, 'PlusMinus'), (0x221E, 'Infinity'), (0x2248, 'Approximately'), (0x2260, 'NotEqual'), (0x2264, 'LessEqual'),
        (0x2265, 'GreaterEqual'), (0x2211, 'Sum'), (0x220F, 'Product'), (0x222B, 'Integral'), (0x2202, 'Partial'),
        (0x0394, 'Delta'), (0x2207, 'Nabla'), (0x221A, 'SquareRoot'), (0x221B, 'CubeRoot'), (0x221C, 'FourthRoot'),
        (0x221D, 'Proportional'), (0x2234, 'Therefore'), (0x2235, 'Because'), (0x2200, 'ForAll'), (0x2203, 'Exists'),

        # Currency and financial
        (0x0024, 'Dollar'), (0x20AC, 'Euro'), (0x00A3, 'Pound'), (0x00A5, 'Yen'), (0x20B9, 'Rupee'),
        (0x20BD, 'Ruble'), (0x20A9, 'Won'), (0x20AA, 'Shekel'), (0x20AB, 'Dong'), (0x20A6, 'Naira'),
        (0x20A1, 'Colon'), (0x20A2, 'Cruzeiro'), (0x20A3, 'Franc'), (0x20A4, 'Lira'), (0x20A5, 'Mill'),
        (0x20A7, 'Peseta'), (0x20A8, 'Rupee'), (0x20B4, 'Hryvnia'), (0x20B5, 'Cedi'), (0x20B8, 'Tenge'),

        # Additional symbols to reach 100
        (0x2665, 'Heart'), (0x2660, 'Spade'), (0x2663, 'Club'), (0x2666, 'Diamond'), (0x2605, 'Star'),
        (0x2606, 'Star'), (0x2600, 'Sun'), (0x2601, 'Cloud'), (0x2602, 'Umbrella'), (0x2603, 'Snowman'),
        (0x260E, 'Phone'), (0x2709, 'Envelope'), (0x270F, 'Pencil'), (0x2712, 'Nib'), (0x2702, 'Scissors'),
        (0x2708, 'Airplane'), (0x2764, 'Heart'), (0x2744, 'Snowflake'), (0x2728, 'Sparkles'), (0x2B50, 'Star'),
    ]

    # Convert to dictionary with actual Unicode characters
    symbol_dict = {}
    for code, meaning in symbol_codes:
        try:
            symbol = chr(code)
            symbol_dict[symbol] = meaning
        except ValueError:
            # Skip invalid Unicode code points
            continue

    return symbol_dict

# Create the symbol test set
SYMBOL_TEST_SET = create_symbol_test_set()

def create_symbol_test_content():
    """Create test content with all 100 symbols"""
    
    # Create HTML table with symbols
    html_rows = []
    
    # Add header
    html_rows.append('<tr><th>Symbol</th><th>Unicode</th><th>Category</th><th>Status</th></tr>')
    
    # Add rows with symbols
    for i, (symbol, meaning) in enumerate(SYMBOL_TEST_SET.items()):
        unicode_code = f'U+{ord(symbol):04X}'
        category = 'Test'
        html_rows.append(f'<tr><td>{symbol}</td><td>{unicode_code}</td><td>{category}</td><td>{symbol}</td></tr>')
    
    table_html = f'''<table>
<caption>100 Symbol Test Table</caption>
{chr(10).join(html_rows)}
</table>'''
    
    return {
        'content': table_html,
        'language': 'en'
    }

def test_encoding_with_symbols(encoding_name, encoding):
    """Test symbol processing with specific encoding"""
    
    print(f"\n=== TESTING {encoding_name.upper()} ENCODING ===")
    
    # Create test content
    test_content = create_symbol_test_content()
    content_str = str(test_content)
    
    # Create temporary file with specified encoding
    try:
        fd, filepath = tempfile.mkstemp(suffix=f'_{encoding}.txt', text=True)
        
        with os.fdopen(fd, 'w', encoding=encoding) as f:
            f.write(content_str)
        
        print(f"✅ Created test file with {encoding_name} encoding")
        
        # Test file reading and encoding detection
        detected_encoding = finalcode2.detect_file_encoding(filepath)
        print(f"📋 Detected encoding: {detected_encoding}")
        
        # Read file content
        with open(filepath, 'r', encoding=detected_encoding) as f:
            file_content = f.read()
        
        # Count symbols in original content
        original_symbols = sum(1 for char in content_str if char in finalcode2.ALL_SYMBOLS)
        file_symbols = sum(1 for char in file_content if char in finalcode2.ALL_SYMBOLS)
        
        print(f"📊 Symbols in original: {original_symbols}")
        print(f"📊 Symbols in file: {file_symbols}")
        print(f"📊 Symbol preservation: {file_symbols/original_symbols*100:.1f}%")
        
        # Test symbol detection
        detected_symbols = finalcode2.detect_unicode_symbols(file_content)
        print(f"🔍 Symbols detected: {len(detected_symbols)}")
        
        # Test symbol replacement
        replaced_content = finalcode2.replace_symbols(file_content)
        
        # Count replacements
        replacement_words = ['Yes', 'No', 'Unknown', 'Warning', 'Up', 'Down', 'Left', 'Right', 'Both']
        replacement_count = sum(replaced_content.count(word) for word in replacement_words)
        
        print(f"🔄 Symbol replacements made: {replacement_count}")
        
        # Test full table processing
        try:
            result = finalcode2.correct_tables(file_content)
            
            if result:
                # Check if result contains replacements
                result_str = str(result)
                result_replacements = sum(result_str.count(word) for word in replacement_words)
                
                print(f"✅ Full processing successful")
                print(f"📈 Final replacements in result: {result_replacements}")
                
                # Calculate success rate
                success_rate = (result_replacements / len(SYMBOL_TEST_SET)) * 100
                print(f"🎯 Overall success rate: {success_rate:.1f}%")
                
                return {
                    'encoding': encoding_name,
                    'symbols_detected': len(detected_symbols),
                    'replacements_made': replacement_count,
                    'final_replacements': result_replacements,
                    'success_rate': success_rate,
                    'status': 'SUCCESS'
                }
            else:
                print(f"❌ Full processing failed")
                return {
                    'encoding': encoding_name,
                    'status': 'FAILED',
                    'error': 'Processing returned None'
                }
                
        except Exception as e:
            print(f"❌ Full processing error: {e}")
            return {
                'encoding': encoding_name,
                'status': 'ERROR',
                'error': str(e)
            }
    
    except Exception as e:
        print(f"❌ Failed to create/test {encoding_name} file: {e}")
        return {
            'encoding': encoding_name,
            'status': 'ERROR',
            'error': str(e)
        }
    
    finally:
        # Clean up
        try:
            os.unlink(filepath)
        except:
            pass

def run_comprehensive_symbol_test():
    """Run comprehensive test with 100 symbols in multiple encodings"""
    
    print("🧪 COMPREHENSIVE 100-SYMBOL TEST")
    print("=" * 50)
    print(f"Testing {len(SYMBOL_TEST_SET)} different Unicode symbols")
    print("Encodings: UTF-8, UTF-16, UTF-16LE, UTF-16BE")
    print()
    
    # Test different encodings
    encodings_to_test = [
        ('UTF-8', 'utf-8'),
        ('UTF-8 with BOM', 'utf-8-sig'),
        ('UTF-16 with BOM', 'utf-16'),
        ('UTF-16 Little Endian', 'utf-16le'),
        ('UTF-16 Big Endian', 'utf-16be'),
    ]
    
    results = []
    
    for encoding_name, encoding in encodings_to_test:
        result = test_encoding_with_symbols(encoding_name, encoding)
        results.append(result)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 COMPREHENSIVE TEST SUMMARY")
    print("=" * 50)
    
    for result in results:
        encoding = result['encoding']
        status = result['status']
        
        if status == 'SUCCESS':
            success_rate = result['success_rate']
            final_replacements = result['final_replacements']
            print(f"✅ {encoding:20} | Success Rate: {success_rate:5.1f}% | Replacements: {final_replacements:3d}")
        else:
            error = result.get('error', 'Unknown error')
            print(f"❌ {encoding:20} | FAILED: {error}")
    
    # Overall statistics
    successful_tests = [r for r in results if r['status'] == 'SUCCESS']
    if successful_tests:
        avg_success_rate = sum(r['success_rate'] for r in successful_tests) / len(successful_tests)
        print(f"\n🎯 Average success rate across all encodings: {avg_success_rate:.1f}%")
        
        if avg_success_rate >= 90:
            print("🏆 EXCELLENT: Symbol handling is robust across all encodings!")
        elif avg_success_rate >= 75:
            print("✅ GOOD: Symbol handling works well with minor issues")
        else:
            print("⚠️  NEEDS IMPROVEMENT: Symbol handling has significant issues")

if __name__ == "__main__":
    run_comprehensive_symbol_test()
