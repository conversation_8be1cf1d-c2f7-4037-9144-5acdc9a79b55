#!/usr/bin/env python3
"""
Compare finalcode1.py vs finalcode2.py outputs
"""

import finalcode1
import finalcode2

def compare_outputs():
    print("=== COMPARISON: finalcode1 vs finalcode2 ===")
    print()
    
    # Test case 1: Simple colspan header table (the issue we fixed)
    test_html1 = '<table><caption>Test Table</caption><tr><th colspan="2">Sales Data</th></tr><tr><th>Product</th><th>Revenue</th></tr><tr><td>Product A</td><td>1000</td></tr></table>'
    
    print("Test 1: Colspan header table")
    print("HTML: Simple table with colspan header")
    print()
    
    print("--- FINALCODE1 RESULT ---")
    try:
        result1 = finalcode1.correct_tables(test_html1)
        print(result1)
    except Exception as e:
        print(f"Error: {e}")
    
    print()
    print("--- FINALCODE2 RESULT ---")
    try:
        result2 = finalcode2.correct_tables(test_html1)
        print(result2)
    except Exception as e:
        print(f"Error: {e}")
    
    print()
    print("=" * 60)
    
    # Test case 2: Regular table (should be same)
    test_html2 = '<table><caption>Regular Table</caption><tr><th>Name</th><th>Age</th></tr><tr><td>John</td><td>25</td></tr></table>'
    
    print("Test 2: Regular table")
    print("HTML: Standard table without colspan")
    print()
    
    print("--- FINALCODE1 RESULT ---")
    try:
        result1 = finalcode1.correct_tables(test_html2)
        print(result1)
    except Exception as e:
        print(f"Error: {e}")
    
    print()
    print("--- FINALCODE2 RESULT ---")
    try:
        result2 = finalcode2.correct_tables(test_html2)
        print(result2)
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    compare_outputs()
