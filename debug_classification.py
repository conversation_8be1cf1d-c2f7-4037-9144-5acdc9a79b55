#!/usr/bin/env python3

import finalcode2

def debug_table_classification():
    """Debug the classification of specific problematic tables."""
    
    # Test Table 6 (should be complex_header_spanning -> nested dict)
    table6_html = '''
    <table>
        <caption>Table 6: a table with a more serious headings problem</caption>
        <tr><td>Rainfall (inches)</td><td colspan="4">2010</td></tr>
        <tr><td></td><td>Americas</td><td>Asia</td><td>Europe</td><td>Africa</td></tr>
        <tr><td>Average</td><td>104</td><td>201</td><td>193</td><td>144</td></tr>
        <tr><td>24 hour high</td><td>15</td><td>26</td><td>27</td><td>18</td></tr>
        <tr><td>12 hour high</td><td>9</td><td>10</td><td>11</td><td>12</td></tr>
        <tr><td>Rainfall (inches)</td><td colspan="4">2009</td></tr>
        <tr><td></td><td>Americas</td><td>Asia</td><td>Europe</td><td>Africa</td></tr>
        <tr><td>Average</td><td>133</td><td>244</td><td>155</td><td>166</td></tr>
        <tr><td>24 hour high</td><td>27</td><td>28</td><td>29</td><td>20</td></tr>
        <tr><td>12 hour high</td><td>11</td><td>12</td><td>13</td><td>16</td></tr>
    </table>
    '''
    
    print("=== DEBUGGING TABLE 6 CLASSIFICATION ===")
    
    # Process the table
    tables = finalcode2.process_tables(table6_html)
    if tables:
        table = tables[0]
        print(f"Table data structure: {table}")
        
        # Get classification
        classification = finalcode2.classify_table_from_data(table)
        print(f"Classification: {classification}")
        
        # Get matrix
        matrix = finalcode2.build_expanded_matrix(table)
        print(f"Matrix dimensions: {len(matrix)} rows x {len(matrix[0]) if matrix else 0} cols")
        print(f"Matrix preview:")
        for i, row in enumerate(matrix[:3]):
            print(f"  Row {i}: {row}")
        
        # Get features
        features = finalcode2.extract_table_features(table)
        print(f"Features: {features}")
        
        # Convert
        result = finalcode2.convert_to_key_value_json(table, classification)
        print(f"Result type: {type(result)}")
        print(f"Result preview: {str(result)[:200]}...")

if __name__ == "__main__":
    debug_table_classification()
