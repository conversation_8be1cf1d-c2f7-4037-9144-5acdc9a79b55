#!/usr/bin/env python3
"""
Comprehensive test for empty row filtering functionality.
Tests various table structures to ensure empty rows are filtered out
while preserving empty columns and other table structures.
"""

import finalcode2
import json

def test_case(name, html_content, expected_row_count):
    """Test a single case and report results"""
    print(f"\n=== {name} ===")
    result = finalcode2.correct_tables(html_content)
    
    # Extract JSON from result
    start_idx = result.find('[')
    if start_idx == -1:
        start_idx = result.find('{')
    end_idx = result.rfind(']') + 1
    if end_idx == 0:
        end_idx = result.rfind('}') + 1
    
    if start_idx != -1 and end_idx != -1:
        json_str = result[start_idx:end_idx]
        try:
            parsed_data = json.loads(json_str)
            if isinstance(parsed_data, list):
                actual_count = len(parsed_data)
            elif isinstance(parsed_data, dict):
                actual_count = 1  # Single row as dict
            else:
                actual_count = 0
                
            status = "✓ PASS" if actual_count == expected_row_count else "✗ FAIL"
            print(f"Expected rows: {expected_row_count}, Actual: {actual_count} - {status}")
            
            if actual_count <= 5:  # Only show details for small tables
                print(f"Data: {parsed_data}")
            return actual_count == expected_row_count
        except json.JSONDecodeError:
            print("✗ FAIL - Could not parse JSON")
            return False
    else:
        print("✗ FAIL - Could not extract JSON")
        return False

def run_comprehensive_tests():
    """Run comprehensive tests for empty row filtering"""
    
    print("=== COMPREHENSIVE EMPTY ROW FILTERING TESTS ===")
    
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Basic table with empty rows
    total_tests += 1
    html1 = """
    <table>
        <tr><th>Name</th><th>Age</th></tr>
        <tr><td>John</td><td>25</td></tr>
        <tr><td></td><td></td></tr>
        <tr><td>Mary</td><td>30</td></tr>
        <tr><td></td><td></td></tr>
    </table>
    """
    if test_case("Basic table with 2 empty rows", html1, 2):
        tests_passed += 1
    
    # Test 2: Table with empty columns but no empty rows
    total_tests += 1
    html2 = """
    <table>
        <tr><th>Name</th><th>Age</th><th>City</th></tr>
        <tr><td>John</td><td></td><td>NYC</td></tr>
        <tr><td>Mary</td><td>30</td><td></td></tr>
    </table>
    """
    if test_case("Table with empty columns (should preserve)", html2, 2):
        tests_passed += 1
    
    # Test 3: Table with mixed empty rows and empty columns
    total_tests += 1
    html3 = """
    <table>
        <tr><th>Product</th><th>Price</th><th>Stock</th></tr>
        <tr><td>Apple</td><td>$1.00</td><td></td></tr>
        <tr><td></td><td></td><td></td></tr>
        <tr><td>Banana</td><td></td><td>20</td></tr>
        <tr><td></td><td></td><td></td></tr>
        <tr><td>Orange</td><td>$0.75</td><td>15</td></tr>
    </table>
    """
    if test_case("Mixed empty rows and columns", html3, 3):
        tests_passed += 1
    
    # Test 4: Single row table (should not be affected)
    total_tests += 1
    html4 = """
    <table>
        <tr><th>Status</th><th>Value</th></tr>
        <tr><td>Active</td><td>Yes</td></tr>
    </table>
    """
    if test_case("Single row table", html4, 1):
        tests_passed += 1
    
    # Test 5: Table with all rows empty except header (should return empty list)
    total_tests += 1
    html5 = """
    <table>
        <tr><th>Name</th><th>Age</th></tr>
        <tr><td></td><td></td></tr>
        <tr><td></td><td></td></tr>
        <tr><td></td><td></td></tr>
    </table>
    """
    if test_case("All data rows empty", html5, 0):
        tests_passed += 1
    
    # Test 6: Complex table with hierarchical structure
    total_tests += 1
    html6 = """
    <table>
        <tr><th>Category</th><th>Item</th><th>Value</th></tr>
        <tr><td>Food</td><td>Apple</td><td>$1</td></tr>
        <tr><td></td><td></td><td></td></tr>
        <tr><td>Food</td><td>Banana</td><td>$0.5</td></tr>
        <tr><td>Drink</td><td>Water</td><td>$2</td></tr>
        <tr><td></td><td></td><td></td></tr>
    </table>
    """
    if test_case("Hierarchical table with empty rows", html6, 3):
        tests_passed += 1
    
    print(f"\n=== SUMMARY ===")
    print(f"Tests passed: {tests_passed}/{total_tests}")
    print(f"Success rate: {tests_passed/total_tests*100:.1f}%")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED! Empty row filtering is working correctly.")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    run_comprehensive_tests()
