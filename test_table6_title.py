#!/usr/bin/env python3
import finalcode2
import re
import json

# Test Table 6 with the new title format
with open('di_parser_output.txt', 'r', encoding='utf-8') as f:
    content = f.read()

tables = re.findall(r'<table>.*?</table>', content, re.DOTALL)

print('=== TESTING TABLE 6 WITH TITLE FORMAT ===')
print()

for table in tables:
    if 'Table 6:' in table:
        processed_tables = finalcode2.process_tables(table)
        if processed_tables:
            table_data = processed_tables[0]
            result = finalcode2.convert_to_key_value_json(table_data, finalcode2.classify_table_from_data(table_data))
            
            print('New format:')
            print(json.dumps(result, indent=2))
            print()
            
            # Show the structure more clearly
            if 'table_title' in result:
                print('✅ SUCCESS: Table title added!')
                print(f'Title: {result["table_title"]}')
                print(f'Data structure: {type(result["data"])}')
                print(f'Data keys: {list(result["data"].keys()) if isinstance(result["data"], dict) else "Not dict"}')
            else:
                print('❌ ISSUE: No table_title found')
            break
