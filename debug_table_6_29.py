#!/usr/bin/env python3

import sys
sys.path.append('.')

def debug_table_6_and_29():
    print("=== DEBUGGING TABLE 6 AND 29 CONVERSION ===")
    
    try:
        from finalcode2 import (
            load_table_data, 
            classify_table_from_data, 
            build_expanded_matrix, 
            convert_to_key_value_json,
            is_year_metric_region_table,
            convert_year_metric_region_table,
            convert_complex_header_spanning_table
        )
        
        data = load_table_data('di_parser_output.txt')
        tables = data.get('content', {}).get('tables', [])
        
        # Test Table 6
        if len(tables) > 5:
            table_6 = tables[5]  # Table 6 is at index 5
            print("\n--- TABLE 6 DEBUG ---")
            print(f"Caption: {table_6.get('caption', 'N/A')}")
            
            # Step 1: Classification
            classification = classify_table_from_data(table_6)
            print(f"1. Classification: {classification}")
            
            # Step 2: Matrix building
            matrix = build_expanded_matrix(table_6)
            print(f"2. Matrix dimensions: {len(matrix)} rows x {len(matrix[0]) if matrix else 0} cols")
            print("3. Matrix preview (first 4 rows):")
            for i, row in enumerate(matrix[:4]):
                print(f"   Row {i}: {row}")
            
            # Step 3: Year-metric-region detection
            is_year_metric = is_year_metric_region_table(matrix)
            print(f"4. Is year-metric-region table: {is_year_metric}")
            
            # Step 4: Direct conversion test
            if is_year_metric:
                direct_result = convert_year_metric_region_table(matrix)
                print(f"5. Direct conversion result type: {type(direct_result)}")
                print(f"6. Direct conversion preview: {str(direct_result)[:200]}...")
            
            # Step 5: Full pipeline result
            result = convert_to_key_value_json(table_6, classification)
            print(f"7. Full pipeline result type: {type(result)}")
            print(f"8. Full pipeline preview: {str(result)[:200]}...")
            
        print("\n" + "="*60)
        
        # Test Table 29
        if len(tables) > 28:
            table_29 = tables[28]  # Table 29 is at index 28
            print("\n--- TABLE 29 DEBUG ---")
            print(f"Caption: {table_29.get('caption', 'N/A')}")
            
            # Step 1: Classification
            classification = classify_table_from_data(table_29)
            print(f"1. Classification: {classification}")
            
            # Step 2: Matrix building
            matrix = build_expanded_matrix(table_29)
            print(f"2. Matrix dimensions: {len(matrix)} rows x {len(matrix[0]) if matrix else 0} cols")
            print("3. Matrix preview (first 4 rows):")
            for i, row in enumerate(matrix[:4]):
                print(f"   Row {i}: {row}")
            
            # Step 3: Year-metric-region detection
            is_year_metric = is_year_metric_region_table(matrix)
            print(f"4. Is year-metric-region table: {is_year_metric}")
            
            # Step 4: Direct conversion test
            if is_year_metric:
                direct_result = convert_year_metric_region_table(matrix)
                print(f"5. Direct conversion result type: {type(direct_result)}")
                print(f"6. Direct conversion preview: {str(direct_result)[:200]}...")
            
            # Step 5: Full pipeline result
            result = convert_to_key_value_json(table_29, classification)
            print(f"7. Full pipeline result type: {type(result)}")
            print(f"8. Full pipeline preview: {str(result)[:200]}...")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_table_6_and_29()
