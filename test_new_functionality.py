#!/usr/bin/env python3
"""
Test script to verify the new multiple th header rows with colspan functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from finalcode2 import correct_tables

def test_multiple_th_header_rows_with_colspan():
    """Test the new functionality for multiple th header rows with colspan."""
    
    # Test case 1: Simple case with 2 th rows where first has colspan equal to total columns
    test_html_1 = '''
    <table>
        <tr>
            <th colspan="3">Sales Report 2024</th>
        </tr>
        <tr>
            <th>Product</th>
            <th>Revenue</th>
            <th>Growth</th>
        </tr>
        <tr>
            <td>Product A</td>
            <td>5000</td>
            <td>12%</td>
        </tr>
        <tr>
            <td>Product B</td>
            <td>3000</td>
            <td>8%</td>
        </tr>
    </table>
    '''
    
    print("Testing case 1: Multiple th header rows with colspan")
    result_1 = correct_tables(test_html_1)
    print("Result 1:")
    print(result_1)
    print("\n" + "="*50 + "\n")
    
    # Test case 2: Case where first row doesn't have colspan equal to total columns (should not trigger)
    test_html_2 = '''
    <table>
        <tr>
            <th colspan="2">Sales Report</th>
            <th>2024</th>
        </tr>
        <tr>
            <th>Product</th>
            <th>Revenue</th>
            <th>Growth</th>
        </tr>
        <tr>
            <td>Product A</td>
            <td>5000</td>
            <td>12%</td>
        </tr>
    </table>
    '''
    
    print("Testing case 2: First row doesn't have full colspan (should not trigger new functionality)")
    result_2 = correct_tables(test_html_2)
    print("Result 2:")
    print(result_2)
    print("\n" + "="*50 + "\n")
    
    # Test case 3: Case with only one th row (should not trigger)
    test_html_3 = '''
    <table>
        <tr>
            <th>Product</th>
            <th>Revenue</th>
            <th>Growth</th>
        </tr>
        <tr>
            <td>Product A</td>
            <td>5000</td>
            <td>12%</td>
        </tr>
    </table>
    '''
    
    print("Testing case 3: Only one th row (should not trigger new functionality)")
    result_3 = correct_tables(test_html_3)
    print("Result 3:")
    print(result_3)
    print("\n" + "="*50 + "\n")

if __name__ == "__main__":
    test_multiple_th_header_rows_with_colspan()
