#!/usr/bin/env python3
"""
Test the new multi-level and hierarchical header functionality.
"""

import finalcode2

def test_new_functionality():
    """Test the new multi-level and hierarchical header processing"""
    
    print("=== TESTING NEW MULTI-LEVEL HEADER FUNCTIONALITY ===\n")
    
    # Test trial28.txt (tables with 1-7 header rows)
    print("Testing trial28.txt...")
    with open('test/trial28.txt', 'r', encoding='utf-8') as f:
        content = f.read()
    
    result = finalcode2.correct_tables(content)
    
    # Count results
    table_count = result.count('<table>')
    title_count = result.count('<title>')
    
    print(f"Tables processed: {table_count}")
    print(f"Title tags created: {title_count}")
    
    # Extract individual tables for analysis
    tables = []
    start = 0
    for i in range(table_count):
        table_start = result.find('<table>', start)
        if table_start == -1:
            break
        table_end = result.find('</table>', table_start) + 8
        tables.append(result[table_start:table_end])
        start = table_end
    
    print(f"\nDetailed analysis:")
    for i, table in enumerate(tables, 1):
        has_title = '<title>' in table
        
        # Extract caption for identification
        caption = ""
        if '<caption>' in table:
            cap_start = table.find('<caption>') + 9
            cap_end = table.find('</caption>')
            caption = table[cap_start:cap_end] if cap_start > 8 and cap_end > cap_start else ""
        
        # Check if it's JSON array or object
        is_array = '[{' in table or (table.count('[') > 0 and table.count('{') > 0)
        is_object = '{' in table and not is_array and not '<tr>' in table
        
        print(f"Table {i}: {caption}")
        print(f"  - Has title: {'✅' if has_title else '❌'}")
        print(f"  - Format: {'Array' if is_array else 'Object' if is_object else 'Other'}")
        
        if has_title:
            title_start = table.find('<title>') + 7
            title_end = table.find('</title>')
            title = table[title_start:title_end] if title_start > 6 and title_end > title_start else ""
            print(f"  - Title: '{title}'")
        
        print()

def test_comprehensive_compatibility():
    """Test that new functionality doesn't break existing comprehensive test"""
    
    print("=== TESTING COMPREHENSIVE COMPATIBILITY ===\n")
    
    # Run a quick test on a few existing files to ensure no regression
    test_files = ['test/trial1.txt', 'test/trial27.txt']
    
    for test_file in test_files:
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            result = finalcode2.correct_tables(content)
            table_count = result.count('<table>')
            
            print(f"{test_file}: {table_count} tables processed ✅")
            
        except Exception as e:
            print(f"{test_file}: ERROR - {e} ❌")

if __name__ == "__main__":
    test_new_functionality()
    test_comprehensive_compatibility()
