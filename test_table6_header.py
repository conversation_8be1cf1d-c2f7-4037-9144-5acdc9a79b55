#!/usr/bin/env python3
import finalcode2
import re

# Test Table 6 with the new header preservation
with open('di_parser_output.txt', 'r', encoding='utf-8') as f:
    content = f.read()

tables = re.findall(r'<table>.*?</table>', content, re.DOTALL)

print('=== TESTING TABLE 6 WITH HEADER PRESERVATION ===')
print()

for table in tables:
    if 'Table 6:' in table:
        # Process it
        processed_tables = finalcode2.process_tables(table)
        if processed_tables:
            table_data = processed_tables[0]
            
            # Get classification and matrix
            classification = finalcode2.classify_table_from_data(table_data)
            matrix = finalcode2.build_expanded_matrix(table_data)
            
            print(f'Classification: {classification}')
            print('Table Header (Row 0):', matrix[0])
            print()
            
            # Test header preservation detection
            should_preserve = finalcode2.should_preserve_table_header(matrix, classification)
            print(f'Should preserve header: {should_preserve}')
            print()
            
            # Show new result
            result = finalcode2.convert_to_key_value_json(table_data, classification)
            print('New result structure:')
            print(f'Type: {type(result)}')
            if isinstance(result, dict):
                if 'table_header' in result:
                    print(f'Table header: {result["table_header"]}')
                    print(f'Data type: {type(result["data"])}')
                    print(f'Data keys: {list(result["data"].keys()) if isinstance(result["data"], dict) else "Not dict"}')
                else:
                    print('No table_header found - using original format')
                    print(f'Keys: {list(result.keys())[:3]}...')
            print()
        break
