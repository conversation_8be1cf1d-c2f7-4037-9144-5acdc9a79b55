#!/usr/bin/env python3
"""
Verification Script for Colspan Header Column Elimination Fix

This script specifically tests the fix for the issue where tables with 
colspan headers followed by individual column headers were losing columns 
during processing.

ISSUE FIXED: Tables with structure like:
- Row 0: Header with colspan="2" (e.g., "Sales Data")  
- Row 1: Individual headers (e.g., "Product", "Revenue")
- Rows 2+: Data rows

Were incorrectly processed and columns were eliminated instead of preserved.

SOLUTION: Added new classification "simple_two_level_header" and corresponding
conversion function that uses Row 1 as actual headers, ignoring Row 0.
"""

import finalcode2

def test_specific_cases():
    """Test specific problematic cases from trial25.txt"""
    
    print("=== COLSPAN HEADER COLUMN ELIMINATION FIX VERIFICATION ===")
    print()
    
    # Test cases that were problematic before the fix
    test_cases = [
        {
            'name': 'Table 1: Basic 2-column case',
            'html': '<table><caption>Table 1: Basic Colspan Header Issue</caption><tr><th colspan="2">Sales Data</th></tr><tr><th>Product</th><th>Revenue</th></tr><tr><td>Product A</td><td>1000</td></tr><tr><td>Product B</td><td>1500</td></tr></table>',
            'expected_columns': ['Product', 'Revenue'],
            'expected_rows': 2
        },
        {
            'name': 'Table 2: 3-column case',
            'html': '<table><caption>Table 2: Three Column Colspan Issue</caption><tr><th colspan="3">Employee Information</th></tr><tr><th>Name</th><th>Department</th><th>Salary</th></tr><tr><td>John</td><td>IT</td><td>50000</td></tr><tr><td>Jane</td><td>HR</td><td>45000</td></tr></table>',
            'expected_columns': ['Name', 'Department', 'Salary'],
            'expected_rows': 2
        },
        {
            'name': 'Table 8: Simple 2-level headers',
            'html': '<table><caption>Table 8: Simple Two-Level Headers</caption><tr><th colspan="2">Contact Information</th></tr><tr><th>Name</th><th>Email</th></tr><tr><td>Alice</td><td><EMAIL></td></tr><tr><td>Bob</td><td><EMAIL></td></tr></table>',
            'expected_columns': ['Name', 'Email'],
            'expected_rows': 2
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"--- Test {i}: {test_case['name']} ---")
        
        try:
            # Process the table
            tables = finalcode2.process_tables(test_case['html'])
            if not tables:
                print(f"❌ FAILED: No tables extracted")
                all_passed = False
                continue
                
            table = tables[0]
            
            # Build matrix
            matrix = finalcode2.build_expanded_matrix(table)
            print(f"Matrix rows: {len(matrix)}")
            for j, row in enumerate(matrix):
                print(f"  Row {j}: {row}")
            
            # Classify and convert
            classification = finalcode2.classify_table_from_data(table)
            print(f"Classification: {classification}")
            
            result = finalcode2.convert_to_key_value_json(table, classification)
            print(f"Result: {result}")
            
            # Verify results
            if isinstance(result, list) and len(result) > 0:
                first_row = result[0]
                actual_columns = list(first_row.keys())
                
                # Check column count and names
                if len(actual_columns) == len(test_case['expected_columns']):
                    print(f"✅ Column count correct: {len(actual_columns)}")
                else:
                    print(f"❌ Column count wrong: expected {len(test_case['expected_columns'])}, got {len(actual_columns)}")
                    all_passed = False
                
                # Check column names
                if set(actual_columns) == set(test_case['expected_columns']):
                    print(f"✅ Column names correct: {actual_columns}")
                else:
                    print(f"❌ Column names wrong: expected {test_case['expected_columns']}, got {actual_columns}")
                    all_passed = False
                
                # Check row count
                if len(result) == test_case['expected_rows']:
                    print(f"✅ Row count correct: {len(result)}")
                else:
                    print(f"❌ Row count wrong: expected {test_case['expected_rows']}, got {len(result)}")
                    all_passed = False
                    
            else:
                print(f"❌ FAILED: Invalid result format")
                all_passed = False
                
        except Exception as e:
            print(f"❌ FAILED: Exception occurred: {e}")
            all_passed = False
        
        print()
    
    # Summary
    print("=" * 60)
    if all_passed:
        print("🎯 ALL TESTS PASSED! Column elimination issue is FIXED!")
        print("✅ Tables with colspan headers now preserve all columns correctly")
    else:
        print("❌ SOME TESTS FAILED! Column elimination issue still exists")
    print("=" * 60)

if __name__ == "__main__":
    test_specific_cases()
