# TYPES OF TABLES HANDLED BY FINALCODE2

## OVERVIEW
The finalcode2 system can process and convert various types of HTML table structures into structured JSON format. Each table type is automatically classified based on its structural patterns and converted using the most appropriate strategy.

## TABLE CLASSIFICATION TYPES

### 1. FUNCTION_SUBFUNCTION_CLEAN
**Description:** Tables with clear functional hierarchy where main categories have subcategories
**Structure Pattern:** Two-level hierarchy with main functions and their subfunctions
**Conversion Strategy:** Hierarchical nested dictionary

**Example:**
```html
<table>
<caption>Department Budget Allocation 2024</caption>
<tr><th>Department</th><th>Subcategory</th><th>Q1</th><th>Q2</th><th>Q3</th><th>Q4</th></tr>
<tr><td rowspan="3">Marketing</td><td>Digital Advertising</td><td>25000</td><td>30000</td><td>28000</td><td>32000</td></tr>
<tr><td>Print Media</td><td>15000</td><td>12000</td><td>18000</td><td>20000</td></tr>
<tr><td>Events</td><td>35000</td><td>40000</td><td>45000</td><td>50000</td></tr>
<tr><td rowspan="2">Research</td><td>Market Analysis</td><td>20000</td><td>22000</td><td>25000</td><td>28000</td></tr>
<tr><td>Product Testing</td><td>18000</td><td>20000</td><td>22000</td><td>24000</td></tr>
</table>
```

**JSON Output:**
```json
{
  "Marketing": {
    "Digital Advertising": {"Q1": "25000", "Q2": "30000", "Q3": "28000", "Q4": "32000"},
    "Print Media": {"Q1": "15000", "Q2": "12000", "Q3": "18000", "Q4": "20000"},
    "Events": {"Q1": "35000", "Q2": "40000", "Q3": "45000", "Q4": "50000"}
  },
  "Research": {
    "Market Analysis": {"Q1": "20000", "Q2": "22000", "Q3": "25000", "Q4": "28000"},
    "Product Testing": {"Q1": "18000", "Q2": "20000", "Q3": "22000", "Q4": "24000"}
  }
}
```

### 2. SIMPLE_GRID_LAYOUT
**Description:** Simple grid tables with regular structure and categorical data
**Structure Pattern:** Subject-based organization with multiple attributes per subject
**Conversion Strategy:** Hierarchical grouping by subject

**Example:**
```html
<table>
<caption>University Course Offerings Spring 2024</caption>
<tr><th>Subject</th><th>Course Code</th><th>Credits</th><th>Prerequisites</th><th>Instructor</th></tr>
<tr><td>Computer Science</td><td>CS101</td><td>3</td><td>None</td><td>Dr. Smith</td></tr>
<tr><td>Computer Science</td><td>CS201</td><td>4</td><td>CS101</td><td>Prof. Johnson</td></tr>
<tr><td>Mathematics</td><td>MATH150</td><td>3</td><td>None</td><td>Dr. Wilson</td></tr>
<tr><td>Mathematics</td><td>MATH250</td><td>4</td><td>MATH150</td><td>Prof. Davis</td></tr>
<tr><td>Physics</td><td>PHYS101</td><td>4</td><td>MATH150</td><td>Dr. Brown</td></tr>
</table>
```

**JSON Output:**
```json
{
  "Computer Science": {
    "CS101": {"Credits": "3", "Prerequisites": "None", "Instructor": "Dr. Smith"},
    "CS201": {"Credits": "4", "Prerequisites": "CS101", "Instructor": "Prof. Johnson"}
  },
  "Mathematics": {
    "MATH150": {"Credits": "3", "Prerequisites": "None", "Instructor": "Dr. Wilson"},
    "MATH250": {"Credits": "4", "Prerequisites": "MATH150", "Instructor": "Prof. Davis"}
  },
  "Physics": {
    "PHYS101": {"Credits": "4", "Prerequisites": "MATH150", "Instructor": "Dr. Brown"}
  }
}
```

### 3. MISALIGNED_BLOCKY_LAYOUT
**Description:** Tables with irregular cell alignment and blocky structure
**Structure Pattern:** Inconsistent cell spans creating block-like appearance
**Conversion Strategy:** Structured list format preserving relationships

**Example:**
```html
<table>
<caption>Project Resource Allocation</caption>
<tr><th colspan="2">Project Alpha</th><th>Budget</th><th>Timeline</th></tr>
<tr><td>Team Lead</td><td>Sarah Connor</td><td rowspan="3">$150,000</td><td rowspan="3">6 months</td></tr>
<tr><td>Developer</td><td>John Doe</td></tr>
<tr><td>Designer</td><td>Jane Smith</td></tr>
<tr><th colspan="2">Project Beta</th><th>Budget</th><th>Timeline</th></tr>
<tr><td>Team Lead</td><td>Mike Johnson</td><td rowspan="2">$200,000</td><td rowspan="2">8 months</td></tr>
<tr><td>Analyst</td><td>Lisa Wang</td></tr>
</table>
```

**JSON Output:**
```json
[
  {"Project": "Project Alpha", "Role": "Team Lead", "Person": "Sarah Connor", "Budget": "$150,000", "Timeline": "6 months"},
  {"Project": "Project Alpha", "Role": "Developer", "Person": "John Doe", "Budget": "$150,000", "Timeline": "6 months"},
  {"Project": "Project Alpha", "Role": "Designer", "Person": "Jane Smith", "Budget": "$150,000", "Timeline": "6 months"},
  {"Project": "Project Beta", "Role": "Team Lead", "Person": "Mike Johnson", "Budget": "$200,000", "Timeline": "8 months"},
  {"Project": "Project Beta", "Role": "Analyst", "Person": "Lisa Wang", "Budget": "$200,000", "Timeline": "8 months"}
]
```

### 4. SPARSE_VERTICAL_LAYOUT
**Description:** Tables with sparse data distribution and vertical organization
**Structure Pattern:** Financial data with subtotal rows and varying cell spans
**Conversion Strategy:** Hierarchical structure preserving financial relationships

**Example:**
```html
<table>
<caption>Quarterly Sales Report 2024</caption>
<tr><th>Region</th><th>Product Line</th><th>Q1 Sales</th><th>Q2 Sales</th></tr>
<tr><td rowspan="3">North America</td><td>Software</td><td>$500,000</td><td>$550,000</td></tr>
<tr><td>Hardware</td><td>$300,000</td><td>$320,000</td></tr>
<tr><td>Services</td><td>$200,000</td><td>$230,000</td></tr>
<tr><td colspan="2">North America Subtotal</td><td>$1,000,000</td><td>$1,100,000</td></tr>
<tr><td rowspan="2">Europe</td><td>Software</td><td>$400,000</td><td>$450,000</td></tr>
<tr><td>Hardware</td><td>$250,000</td><td>$280,000</td></tr>
<tr><td colspan="2">Europe Subtotal</td><td>$650,000</td><td>$730,000</td></tr>
</table>
```

**JSON Output:**
```json
{
  "North America": {
    "Software": {"Q1 Sales": "$500,000", "Q2 Sales": "$550,000"},
    "Hardware": {"Q1 Sales": "$300,000", "Q2 Sales": "$320,000"},
    "Services": {"Q1 Sales": "$200,000", "Q2 Sales": "$230,000"},
    "Subtotal": {"Q1 Sales": "$1,000,000", "Q2 Sales": "$1,100,000"}
  },
  "Europe": {
    "Software": {"Q1 Sales": "$400,000", "Q2 Sales": "$450,000"},
    "Hardware": {"Q1 Sales": "$250,000", "Q2 Sales": "$280,000"},
    "Subtotal": {"Q1 Sales": "$650,000", "Q2 Sales": "$730,000"}
  }
}
```

### 5. GROUPED_SUMMARY
**Description:** Tables with grouped data and summary information
**Structure Pattern:** Data grouped by categories with summary rows
**Conversion Strategy:** Grouped hierarchical structure

**Example:**
```html
<table>
<caption>Employee Performance Summary 2024</caption>
<tr><th>Department</th><th>Employee</th><th>Projects Completed</th><th>Rating</th></tr>
<tr><td rowspan="3">Engineering</td><td>Alice Johnson</td><td>12</td><td>Excellent</td></tr>
<tr><td>Bob Wilson</td><td>10</td><td>Good</td></tr>
<tr><td>Carol Davis</td><td>15</td><td>Outstanding</td></tr>
<tr><td rowspan="2">Marketing</td><td>David Brown</td><td>8</td><td>Good</td></tr>
<tr><td>Eva Martinez</td><td>11</td><td>Excellent</td></tr>
<tr><td rowspan="2">Sales</td><td>Frank Miller</td><td>20</td><td>Outstanding</td></tr>
<tr><td>Grace Lee</td><td>18</td><td>Excellent</td></tr>
</table>
```

**JSON Output:**
```json
{
  "Engineering": {
    "Alice Johnson": {"Projects Completed": "12", "Rating": "Excellent"},
    "Bob Wilson": {"Projects Completed": "10", "Rating": "Good"},
    "Carol Davis": {"Projects Completed": "15", "Rating": "Outstanding"}
  },
  "Marketing": {
    "David Brown": {"Projects Completed": "8", "Rating": "Good"},
    "Eva Martinez": {"Projects Completed": "11", "Rating": "Excellent"}
  },
  "Sales": {
    "Frank Miller": {"Projects Completed": "20", "Rating": "Outstanding"},
    "Grace Lee": {"Projects Completed": "18", "Rating": "Excellent"}
  }
}
```

### 6. WIDE_COLUMN_GROUPED_LAYOUT
**Description:** Tables with wide column spans and grouped data organization
**Structure Pattern:** Time-based grouping with multiple data points per period
**Conversion Strategy:** Temporal hierarchical structure

**Example:**
```html
<table>
<caption>Website Traffic Analytics 2024</caption>
<tr><th>Month</th><th>Metric</th><th>Desktop</th><th>Mobile</th><th>Tablet</th></tr>
<tr><td rowspan="3">January</td><td>Page Views</td><td>45000</td><td>32000</td><td>8000</td></tr>
<tr><td>Unique Visitors</td><td>12000</td><td>9500</td><td>2100</td></tr>
<tr><td>Bounce Rate</td><td>35%</td><td>42%</td><td>38%</td></tr>
<tr><td rowspan="3">February</td><td>Page Views</td><td>52000</td><td>38000</td><td>9500</td></tr>
<tr><td>Unique Visitors</td><td>14500</td><td>11200</td><td>2400</td></tr>
<tr><td>Bounce Rate</td><td>32%</td><td>39%</td><td>35%</td></tr>
</table>
```

**JSON Output:**
```json
{
  "January": {
    "Page Views": {"Desktop": "45000", "Mobile": "32000", "Tablet": "8000"},
    "Unique Visitors": {"Desktop": "12000", "Mobile": "9500", "Tablet": "2100"},
    "Bounce Rate": {"Desktop": "35%", "Mobile": "42%", "Tablet": "38%"}
  },
  "February": {
    "Page Views": {"Desktop": "52000", "Mobile": "38000", "Tablet": "9500"},
    "Unique Visitors": {"Desktop": "14500", "Mobile": "11200", "Tablet": "2400"},
    "Bounce Rate": {"Desktop": "32%", "Mobile": "39%", "Tablet": "35%"}
  }
}
```

### 7. SECTIONED_MULTICOLUMN_LAYOUT
**Description:** Complex tables with multiple sections and varied column structures
**Structure Pattern:** Multiple distinct sections with different column arrangements
**Conversion Strategy:** Sectioned hierarchical structure

**Example:**
```html
<table>
<caption>Company Financial Overview 2024</caption>
<tr><th colspan="3">Revenue Streams</th></tr>
<tr><th>Source</th><th>Q1</th><th>Q2</th></tr>
<tr><td>Product Sales</td><td>$2,500,000</td><td>$2,800,000</td></tr>
<tr><td>Service Contracts</td><td>$1,200,000</td><td>$1,350,000</td></tr>
<tr><th colspan="3">Operating Expenses</th></tr>
<tr><th>Category</th><th>Q1</th><th>Q2</th></tr>
<tr><td>Personnel</td><td>$1,800,000</td><td>$1,900,000</td></tr>
<tr><td>Infrastructure</td><td>$400,000</td><td>$420,000</td></tr>
<tr><td>Marketing</td><td>$300,000</td><td>$350,000</td></tr>
</table>
```

**JSON Output:**
```json
{
  "Revenue Streams": {
    "Product Sales": {"Q1": "$2,500,000", "Q2": "$2,800,000"},
    "Service Contracts": {"Q1": "$1,200,000", "Q2": "$1,350,000"}
  },
  "Operating Expenses": {
    "Personnel": {"Q1": "$1,800,000", "Q2": "$1,900,000"},
    "Infrastructure": {"Q1": "$400,000", "Q2": "$420,000"},
    "Marketing": {"Q1": "$300,000", "Q2": "$350,000"}
  }
}
```

### 8. SIMPLE_STRUCTURED
**Description:** Basic tables with consistent row-column structure
**Structure Pattern:** Simple grid layout with consistent headers
**Conversion Strategy:** List of dictionaries format

**Example:**
```html
<table>
<caption>Product Inventory Status</caption>
<tr><th>Product ID</th><th>Product Name</th><th>Stock Level</th><th>Unit Price</th><th>Supplier</th></tr>
<tr><td>P001</td><td>Wireless Mouse</td><td>150</td><td>$25.99</td><td>TechSupply Co</td></tr>
<tr><td>P002</td><td>USB Keyboard</td><td>85</td><td>$45.50</td><td>KeyBoard Inc</td></tr>
<tr><td>P003</td><td>Monitor Stand</td><td>42</td><td>$89.99</td><td>Desk Solutions</td></tr>
<tr><td>P004</td><td>Webcam HD</td><td>67</td><td>$129.00</td><td>Vision Tech</td></tr>
<tr><td>P005</td><td>Desk Lamp</td><td>23</td><td>$34.75</td><td>Light Works</td></tr>
</table>
```

**JSON Output:**
```json
[
  {"Product ID": "P001", "Product Name": "Wireless Mouse", "Stock Level": "150", "Unit Price": "$25.99", "Supplier": "TechSupply Co"},
  {"Product ID": "P002", "Product Name": "USB Keyboard", "Stock Level": "85", "Unit Price": "$45.50", "Supplier": "KeyBoard Inc"},
  {"Product ID": "P003", "Product Name": "Monitor Stand", "Stock Level": "42", "Unit Price": "$89.99", "Supplier": "Desk Solutions"},
  {"Product ID": "P004", "Product Name": "Webcam HD", "Stock Level": "67", "Unit Price": "$129.00", "Supplier": "Vision Tech"},
  {"Product ID": "P005", "Product Name": "Desk Lamp", "Stock Level": "23", "Unit Price": "$34.75", "Supplier": "Light Works"}
]
```

### 9. HEADER_HEAVY
**Description:** Tables with extensive header structures and minimal data
**Structure Pattern:** Multiple header rows with complex hierarchy
**Conversion Strategy:** Hierarchical structure emphasizing header relationships

**Example:**
```html
<table>
<caption>Global Sales Performance Matrix</caption>
<tr><th rowspan="2">Region</th><th colspan="2">Q1 2024</th><th colspan="2">Q2 2024</th></tr>
<tr><th>Target</th><th>Actual</th><th>Target</th><th>Actual</th></tr>
<tr><td>North America</td><td>$5M</td><td>$5.2M</td><td>$5.5M</td><td>$5.8M</td></tr>
<tr><td>Europe</td><td>$3.5M</td><td>$3.7M</td><td>$4M</td><td>$4.1M</td></tr>
<tr><td>Asia Pacific</td><td>$2.8M</td><td>$3.1M</td><td>$3.2M</td><td>$3.4M</td></tr>
</table>
```

**JSON Output:**
```json
{
  "North America": {
    "Q1 2024": {"Target": "$5M", "Actual": "$5.2M"},
    "Q2 2024": {"Target": "$5.5M", "Actual": "$5.8M"}
  },
  "Europe": {
    "Q1 2024": {"Target": "$3.5M", "Actual": "$3.7M"},
    "Q2 2024": {"Target": "$4M", "Actual": "$4.1M"}
  },
  "Asia Pacific": {
    "Q1 2024": {"Target": "$2.8M", "Actual": "$3.1M"},
    "Q2 2024": {"Target": "$3.2M", "Actual": "$3.4M"}
  }
}
```

### 10. DATA_HEAVY
**Description:** Tables with minimal headers but extensive data content
**Structure Pattern:** Simple header structure with large amounts of data
**Conversion Strategy:** Efficient list format for data-centric tables

**Example:**
```html
<table>
<caption>Daily Temperature Readings - Weather Station Alpha</caption>
<tr><th>Date</th><th>Min Temp (°C)</th><th>Max Temp (°C)</th><th>Humidity (%)</th><th>Wind Speed (km/h)</th></tr>
<tr><td>2024-03-01</td><td>8.2</td><td>18.5</td><td>65</td><td>12.3</td></tr>
<tr><td>2024-03-02</td><td>9.1</td><td>19.8</td><td>58</td><td>15.7</td></tr>
<tr><td>2024-03-03</td><td>7.5</td><td>17.2</td><td>72</td><td>8.9</td></tr>
<tr><td>2024-03-04</td><td>10.3</td><td>21.1</td><td>55</td><td>18.2</td></tr>
<tr><td>2024-03-05</td><td>11.8</td><td>22.4</td><td>48</td><td>22.1</td></tr>
<tr><td>2024-03-06</td><td>9.7</td><td>20.3</td><td>61</td><td>14.5</td></tr>
<tr><td>2024-03-07</td><td>8.9</td><td>19.1</td><td>67</td><td>11.8</td></tr>
</table>
```

**JSON Output:**
```json
[
  {"Date": "2024-03-01", "Min Temp (°C)": "8.2", "Max Temp (°C)": "18.5", "Humidity (%)": "65", "Wind Speed (km/h)": "12.3"},
  {"Date": "2024-03-02", "Min Temp (°C)": "9.1", "Max Temp (°C)": "19.8", "Humidity (%)": "58", "Wind Speed (km/h)": "15.7"},
  {"Date": "2024-03-03", "Min Temp (°C)": "7.5", "Max Temp (°C)": "17.2", "Humidity (%)": "72", "Wind Speed (km/h)": "8.9"},
  {"Date": "2024-03-04", "Min Temp (°C)": "10.3", "Max Temp (°C)": "21.1", "Humidity (%)": "55", "Wind Speed (km/h)": "18.2"},
  {"Date": "2024-03-05", "Min Temp (°C)": "11.8", "Max Temp (°C)": "22.4", "Humidity (%)": "48", "Wind Speed (km/h)": "22.1"},
  {"Date": "2024-03-06", "Min Temp (°C)": "9.7", "Max Temp (°C)": "20.3", "Humidity (%)": "61", "Wind Speed (km/h)": "14.5"},
  {"Date": "2024-03-07", "Min Temp (°C)": "8.9", "Max Temp (°C)": "19.1", "Humidity (%)": "67", "Wind Speed (km/h)": "11.8"}
]
```

### 11. OTHER
**Description:** Tables with unique or irregular structures that don't fit standard patterns
**Structure Pattern:** Non-standard layouts requiring flexible processing
**Conversion Strategy:** Adaptive conversion based on detected structure

**Example:**
```html
<table>
<caption>Software License Matrix</caption>
<tr><th>Software</th><th colspan="3">License Types Available</th><th>Support Level</th></tr>
<tr><td rowspan="2">Database Pro</td><td>Standard</td><td>Enterprise</td><td>Developer</td><td rowspan="2">24/7</td></tr>
<tr><td>$299</td><td>$1,299</td><td>$99</td></tr>
<tr><td rowspan="2">Analytics Suite</td><td>Basic</td><td>Professional</td><td>-</td><td rowspan="2">Business Hours</td></tr>
<tr><td>$199</td><td>$899</td><td>-</td></tr>
<tr><td rowspan="2">Design Tools</td><td>Individual</td><td>Team</td><td>Student</td><td rowspan="2">Community</td></tr>
<tr><td>$29/month</td><td>$99/month</td><td>Free</td></tr>
</table>
```

**JSON Output:**
```json
{
  "Database Pro": {
    "License Types": {"Standard": "$299", "Enterprise": "$1,299", "Developer": "$99"},
    "Support Level": "24/7"
  },
  "Analytics Suite": {
    "License Types": {"Basic": "$199", "Professional": "$899"},
    "Support Level": "Business Hours"
  },
  "Design Tools": {
    "License Types": {"Individual": "$29/month", "Team": "$99/month", "Student": "Free"},
    "Support Level": "Community"
  }
}
```

## SPECIAL FEATURES

### UNICODE SYMBOL PROCESSING
The system automatically detects and replaces Unicode symbols with semantic text:

**Symbol Categories Processed:**
- ✓ Checkmark symbols → "Yes" or "Correct"
- ✗ Cross symbols → "No" or "Incorrect"
- ☐ Checkbox symbols → "No" or "Unchecked"
- ☑ Checked box symbols → "Yes" or "Checked"
- ○ Neutral symbols → Context-dependent replacement

**Example with Symbols:**
```html
<table>
<caption>Feature Availability Matrix</caption>
<tr><th>Feature</th><th>Basic Plan</th><th>Pro Plan</th><th>Enterprise</th></tr>
<tr><td>Email Support</td><td>✓</td><td>✓</td><td>✓</td></tr>
<tr><td>Phone Support</td><td>✗</td><td>✓</td><td>✓</td></tr>
<tr><td>24/7 Support</td><td>✗</td><td>✗</td><td>✓</td></tr>
<tr><td>Custom Integration</td><td>✗</td><td>☐</td><td>✓</td></tr>
</table>
```

**JSON Output with Symbol Replacement:**
```json
[
  {"Feature": "Email Support", "Basic Plan": "Yes", "Pro Plan": "Yes", "Enterprise": "Yes"},
  {"Feature": "Phone Support", "Basic Plan": "No", "Pro Plan": "Yes", "Enterprise": "Yes"},
  {"Feature": "24/7 Support", "Basic Plan": "No", "Pro Plan": "No", "Enterprise": "Yes"},
  {"Feature": "Custom Integration", "Basic Plan": "No", "Pro Plan": "No", "Enterprise": "Yes"}
]
```

## CLASSIFICATION ALGORITHM
The system uses structural analysis to automatically classify tables:

1. **Rowspan/Colspan Analysis:** Detects merged cells and hierarchical patterns
2. **Header Structure Examination:** Identifies header complexity and organization
3. **Data Pattern Recognition:** Analyzes data distribution and relationships
4. **Content Semantic Analysis:** Determines logical groupings and categories
5. **Layout Geometry Assessment:** Evaluates table shape and cell arrangements

## OUTPUT CHARACTERISTICS
- **Caption Preservation:** All table captions wrapped in `<caption>` tags
- **Tag Wrapping:** Each table wrapped in `<table></table>` tags
- **Single-line Format:** Compact JSON without unnecessary whitespace
- **Unicode Normalization:** Symbols replaced with readable text
- **Structure Preservation:** Original table relationships maintained in JSON
- **Error Resilience:** Invalid tables skipped without stopping processing
