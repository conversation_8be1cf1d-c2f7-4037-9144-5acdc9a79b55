#!/usr/bin/env python3
"""
Test additional edge cases for the new functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from finalcode2 import correct_tables

def test_additional_cases():
    """Test additional edge cases."""
    
    print("=== ADDITIONAL TEST CASES ===\n")
    
    # Test case 1: Mixed th and td in first row (should not trigger)
    test_html_1 = '''
    <table>
        <tr>
            <th colspan="2">Sales</th>
            <td>Data</td>
        </tr>
        <tr>
            <th>Region</th>
            <th>Q1</th>
            <th>Q2</th>
        </tr>
        <tr>
            <td>North</td>
            <td>100</td>
            <td>120</td>
        </tr>
    </table>
    '''
    
    print("Test 1: Mixed th and td in first row (should NOT trigger)")
    result_1 = correct_tables(test_html_1)
    if '<title>' not in result_1:
        print("✓ PASS: Mixed th/td does not trigger title tag")
    else:
        print("✗ FAIL: Mixed th/td incorrectly triggered title tag")
    print()
    
    # Test case 2: Second row has no th elements (should not trigger)
    test_html_2 = '''
    <table>
        <tr>
            <th colspan="3">Sales Report</th>
        </tr>
        <tr>
            <td>Region</td>
            <td>Q1</td>
            <td>Q2</td>
        </tr>
        <tr>
            <td>North</td>
            <td>100</td>
            <td>120</td>
        </tr>
    </table>
    '''
    
    print("Test 2: Second row has no th elements (should NOT trigger)")
    result_2 = correct_tables(test_html_2)
    if '<title>' not in result_2:
        print("✓ PASS: No th in second row does not trigger title tag")
    else:
        print("✗ FAIL: No th in second row incorrectly triggered title tag")
    print()
    
    # Test case 3: Perfect match with caption (should trigger and preserve caption)
    test_html_3 = '''
    <table>
        <caption>Financial Data</caption>
        <tr>
            <th colspan="3">Annual Report 2024</th>
        </tr>
        <tr>
            <th>Department</th>
            <th>Budget</th>
            <th>Actual</th>
        </tr>
        <tr>
            <td>Marketing</td>
            <td>50000</td>
            <td>48000</td>
        </tr>
        <tr>
            <td>Sales</td>
            <td>75000</td>
            <td>82000</td>
        </tr>
    </table>
    '''
    
    print("Test 3: Perfect match with caption (should trigger and preserve both)")
    result_3 = correct_tables(test_html_3)
    has_title = '<title>Annual Report 2024</title>' in result_3
    has_caption = '<caption>Financial Data</caption>' in result_3
    has_data = '"Department":"Marketing"' in result_3
    
    if has_title and has_caption and has_data:
        print("✓ PASS: Both caption and title preserved, data correct")
    else:
        print(f"✗ FAIL: title={has_title}, caption={has_caption}, data={has_data}")
    print()
    
    # Test case 4: Empty first th cell (should still trigger if colspan is correct)
    test_html_4 = '''
    <table>
        <tr>
            <th colspan="2"></th>
        </tr>
        <tr>
            <th>Col1</th>
            <th>Col2</th>
        </tr>
        <tr>
            <td>Data1</td>
            <td>Data2</td>
        </tr>
    </table>
    '''
    
    print("Test 4: Empty first th cell (should trigger but no title content)")
    result_4 = correct_tables(test_html_4)
    has_title_tag = '<title>' in result_4
    has_data = '"Col1":"Data1"' in result_4
    
    if not has_title_tag and has_data:
        print("✓ PASS: Empty th does not create title tag, data processed correctly")
    elif has_title_tag and has_data:
        print("✓ PASS: Empty th creates empty title tag, data processed correctly")
    else:
        print(f"✗ FAIL: title_tag={has_title_tag}, data={has_data}")
    print()

if __name__ == "__main__":
    test_additional_cases()
