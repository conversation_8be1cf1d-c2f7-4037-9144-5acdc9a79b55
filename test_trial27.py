#!/usr/bin/env python3
"""
Test the new trial27.txt file with multiple headers.
"""

import finalcode2

def test_trial27():
    """Test trial27.txt with 10 tables having multiple headers"""
    
    with open('test/trial27.txt', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("Testing trial27.txt with 10 multiple header tables...")
    result = finalcode2.correct_tables(content)
    
    # Count tables
    table_count = result.count('<table>')
    title_count = result.count('<title>')
    
    print(f"Tables processed: {table_count}")
    print(f"Title tags created: {title_count}")
    
    if title_count == 10:
        print("✅ SUCCESS: All 10 tables got title tags from first header row!")
    else:
        print(f"❌ ISSUE: Expected 10 title tags, got {title_count}")
    
    # Show first table as example
    if '<table>' in result:
        first_table_start = result.find('<table>')
        first_table_end = result.find('</table>', first_table_start) + 8
        first_table = result[first_table_start:first_table_end]
        
        print("\nFirst table example:")
        print(first_table)
        
        if '<title>' in first_table:
            print("✅ First table has title tag")
        else:
            print("❌ First table missing title tag")

if __name__ == "__main__":
    test_trial27()
