#!/usr/bin/env python3
import finalcode2

# Test multiple header rows directly
test_html = '<table><caption>Table 1: Basic Two Header Rows</caption><tr><th>Sales Report</th><th>Q1 2024</th><th>Performance</th></tr><tr><th>Product</th><th>Revenue</th><th>Growth</th></tr><tr><td>Product A</td><td>5000</td><td>12%</td></tr><tr><td>Product B</td><td>3000</td><td>8%</td></tr></table>'

print('Testing multiple header rows...')
print()

# Process it
tables = finalcode2.process_tables(test_html)
if tables:
    table = tables[0]
    print('Raw structure:')
    for i, row in enumerate(table['with_span']):
        row_texts = [cell['text'] for cell in row]
        print(f'Row {i}: {row_texts}')
    print()
    
    # Build matrix
    matrix = finalcode2.build_expanded_matrix(table)
    print('Matrix:')
    for i, row in enumerate(matrix):
        print(f'Row {i}: {row}')
    print()
    
    # Classify and convert
    classification = finalcode2.classify_table_from_data(table)
    print(f'Classification: {classification}')
    
    result = finalcode2.convert_to_key_value_json(table, classification)
    print(f'Current result: {result}')
    print()
    
    print('Expected result should be:')
    expected = [{"Product": "Product A", "Revenue": "5000", "Growth": "12%"}, {"Product": "Product B", "Revenue": "3000", "Growth": "8%"}]
    print(expected)
