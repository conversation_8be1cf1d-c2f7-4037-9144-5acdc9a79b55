#!/usr/bin/env python3
"""
Test script to demonstrate the specific requirement:
When there are multiple headers (2 tr tag sets with th elements) and the first set has 
colspan equal to the number of columns, then:
1. First set is put in title of table
2. Second set has column headers
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from finalcode2 import correct_tables, has_multiple_th_header_rows_with_colspan, classify_table_from_data, process_tables

def test_specific_requirement():
    """Test the specific requirement with detailed analysis."""
    
    # Test case: 2 tr tag sets with th elements, first has colspan=3 (total columns)
    test_html = '''
    <table>
        <tr>
            <th colspan="3">Quarterly Sales Report 2024</th>
        </tr>
        <tr>
            <th>Region</th>
            <th>Q1 Sales</th>
            <th>Q2 Sales</th>
        </tr>
        <tr>
            <td>North</td>
            <td>150000</td>
            <td>175000</td>
        </tr>
        <tr>
            <td>South</td>
            <td>120000</td>
            <td>140000</td>
        </tr>
        <tr>
            <td>East</td>
            <td>180000</td>
            <td>200000</td>
        </tr>
    </table>
    '''
    
    print("=== TESTING SPECIFIC REQUIREMENT ===")
    print("Input HTML:")
    print(test_html)
    print("\n" + "-"*50 + "\n")
    
    # Step 1: Extract table data
    tables = process_tables(test_html)
    if tables:
        table = tables[0]
        print("Step 1: Table extracted successfully")
        print(f"Table has {len(table['with_span'])} rows")
        
        # Step 2: Check if it matches our pattern
        matches_pattern = has_multiple_th_header_rows_with_colspan(table)
        print(f"Step 2: Matches multiple th header rows with colspan pattern: {matches_pattern}")
        
        # Step 3: Check classification
        classification = classify_table_from_data(table)
        print(f"Step 3: Table classification: {classification}")
        
        # Step 4: Process the table
        result = correct_tables(test_html)
        print(f"Step 4: Final result:")
        print(result)
        
        # Step 5: Verify the result
        print("\n" + "-"*50 + "\n")
        print("=== VERIFICATION ===")
        
        if '<title>Quarterly Sales Report 2024</title>' in result:
            print("✓ PASS: First th row content is in title tag")
        else:
            print("✗ FAIL: First th row content not found in title tag")
        
        if '"Region":"North"' in result and '"Q1 Sales":"150000"' in result:
            print("✓ PASS: Second th row is used as column headers")
        else:
            print("✗ FAIL: Second th row not used as column headers")
        
        if result.count('<table>') == 1 and result.count('</table>') == 1:
            print("✓ PASS: Table structure preserved")
        else:
            print("✗ FAIL: Table structure not preserved")
            
    else:
        print("ERROR: Could not extract table")

def test_edge_cases():
    """Test edge cases to ensure robustness."""
    
    print("\n\n=== TESTING EDGE CASES ===")
    
    # Edge case 1: First row has partial colspan (should not trigger)
    test_html_1 = '''
    <table>
        <tr>
            <th colspan="2">Sales</th>
            <th>2024</th>
        </tr>
        <tr>
            <th>Region</th>
            <th>Q1</th>
            <th>Q2</th>
        </tr>
        <tr>
            <td>North</td>
            <td>100</td>
            <td>120</td>
        </tr>
    </table>
    '''
    
    print("Edge case 1: First row has partial colspan (should NOT trigger)")
    result_1 = correct_tables(test_html_1)
    if '<title>' not in result_1:
        print("✓ PASS: Partial colspan does not trigger title tag")
    else:
        print("✗ FAIL: Partial colspan incorrectly triggered title tag")
    
    # Edge case 2: Only one th row (should not trigger)
    test_html_2 = '''
    <table>
        <tr>
            <th>Region</th>
            <th>Q1</th>
            <th>Q2</th>
        </tr>
        <tr>
            <td>North</td>
            <td>100</td>
            <td>120</td>
        </tr>
    </table>
    '''
    
    print("Edge case 2: Only one th row (should NOT trigger)")
    result_2 = correct_tables(test_html_2)
    if '<title>' not in result_2:
        print("✓ PASS: Single th row does not trigger title tag")
    else:
        print("✗ FAIL: Single th row incorrectly triggered title tag")
    
    # Edge case 3: Three th rows (should not trigger)
    test_html_3 = '''
    <table>
        <tr>
            <th colspan="2">Sales Report</th>
        </tr>
        <tr>
            <th colspan="2">2024 Data</th>
        </tr>
        <tr>
            <th>Region</th>
            <th>Sales</th>
        </tr>
        <tr>
            <td>North</td>
            <td>100</td>
        </tr>
    </table>
    '''
    
    print("Edge case 3: Three th rows (should NOT trigger)")
    result_3 = correct_tables(test_html_3)
    if '<title>' not in result_3:
        print("✓ PASS: Three th rows does not trigger title tag")
    else:
        print("✗ FAIL: Three th rows incorrectly triggered title tag")

if __name__ == "__main__":
    test_specific_requirement()
    test_edge_cases()
