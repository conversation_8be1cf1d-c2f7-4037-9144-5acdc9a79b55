#!/usr/bin/env python3
"""
Debug script to identify why test_symbol.txt works differently on different machines.
"""

import sys
import json
import finalcode2

def debug_symbol_processing():
    """Debug the symbol processing step by step"""
    
    print("=== SYMBOL PROCESSING DEBUG ===\n")
    
    # Step 1: Check Python version and encoding
    print(f"Python version: {sys.version}")
    print(f"Default encoding: {sys.getdefaultencoding()}")
    print(f"File system encoding: {sys.getfilesystemencoding()}")
    print()
    
    # Step 2: Read the test file
    print("Reading test_symbol.txt...")
    try:
        with open('test/test_symbol.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"File read successfully, length: {len(content)}")
        print(f"First 200 chars: {repr(content[:200])}")
        print()
    except Exception as e:
        print(f"ERROR reading file: {e}")
        return
    
    # Step 3: Check symbol detection
    print("Checking symbol detection...")
    symbols_in_content = []
    for char in content:
        if char in finalcode2.ALL_SYMBOLS:
            symbols_in_content.append(char)
    
    print(f"Symbols found in content: {symbols_in_content}")
    print(f"Symbol codes: {[f'U+{ord(c):04X}' for c in symbols_in_content]}")
    print()
    
    # Step 4: Test symbol replacement
    print("Testing symbol replacement...")
    for symbol in set(symbols_in_content):
        replacement = finalcode2.get_replacement_for_symbol(symbol)
        print(f"'{symbol}' (U+{ord(symbol):04X}) → '{replacement}'")
    print()
    
    # Step 5: Test full processing
    print("Testing full processing...")
    try:
        result = finalcode2.correct_tables(content)
        print("Processing successful!")
        
        # Extract just the JSON part
        if isinstance(result, dict) and 'content' in result:
            json_content = result['content']
            if '<table>' in json_content:
                json_start = json_content.find('[')
                if json_start == -1:
                    json_start = json_content.find('{')
                json_end = json_content.rfind(']')
                if json_end == -1:
                    json_end = json_content.rfind('}') + 1
                
                if json_start != -1 and json_end != -1:
                    json_part = json_content[json_start:json_end]
                    print(f"JSON result: {json_part}")
                    
                    # Check for symbol replacements in result
                    has_yes = 'Yes' in json_part
                    has_no = 'No' in json_part
                    has_symbols = any(s in json_part for s in finalcode2.ALL_SYMBOLS)
                    
                    print(f"Contains 'Yes': {has_yes}")
                    print(f"Contains 'No': {has_no}")
                    print(f"Still contains symbols: {has_symbols}")
        
    except Exception as e:
        print(f"ERROR in processing: {e}")
        import traceback
        traceback.print_exc()

def create_test_case():
    """Create a simple test case to isolate the issue"""
    
    print("\n=== CREATING SIMPLE TEST CASE ===\n")
    
    simple_test = {
        'content': '<table><tr><th>Test</th></tr><tr><td>☒</td></tr></table>',
        'language': 'en'
    }
    
    print(f"Simple test input: {simple_test}")
    
    try:
        result = finalcode2.correct_tables(simple_test)
        print(f"Simple test result: {result}")
        
        if '☒' in str(result):
            print("❌ ISSUE: Symbol not replaced")
        elif 'Yes' in str(result):
            print("✅ SUCCESS: Symbol replaced correctly")
        else:
            print("❓ UNCLEAR: Unexpected result")
            
    except Exception as e:
        print(f"ERROR in simple test: {e}")

if __name__ == "__main__":
    debug_symbol_processing()
    create_test_case()
    
    print("\n=== RECOMMENDATIONS FOR YOUR COLLEAGUE ===")
    print("1. Check Python version (should be 3.7+)")
    print("2. Check file encoding (should be UTF-8)")
    print("3. Try: pip install --upgrade beautifulsoup4 lxml")
    print("4. Check locale settings: python -c 'import locale; print(locale.getpreferredencoding())'")
    print("5. Share the output of this debug script")
