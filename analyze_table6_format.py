#!/usr/bin/env python3
import finalcode2
import re
import json

# Analyze Table 6's current format in detail
with open('di_parser_output.txt', 'r', encoding='utf-8') as f:
    content = f.read()

tables = re.findall(r'<table>.*?</table>', content, re.DOTALL)

print('=== CURRENT TABLE 6 FORMAT ANALYSIS ===')
print()

for table in tables:
    if 'Table 6:' in table:
        processed_tables = finalcode2.process_tables(table)
        if processed_tables:
            table_data = processed_tables[0]
            result = finalcode2.convert_to_key_value_json(table_data, finalcode2.classify_table_from_data(table_data))
            
            print('Current format:')
            print(json.dumps(result, indent=2))
            print()
            
            # Show the structure more clearly
            if 'table_header' in result:
                print('Header analysis:')
                header = result['table_header']
                print(f'  Title part: "{header[0]}"')
                print(f'  Column headers: {header[1:]}')
                print()
                
                print('Data structure:')
                data = result['data']
                for year, year_data in data.items():
                    print(f'  {year}:')
                    for metric, values in year_data.items():
                        print(f'    {metric}: {values}')
                    break  # Just show first year
            break
