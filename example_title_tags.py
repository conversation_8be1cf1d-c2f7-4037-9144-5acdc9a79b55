#!/usr/bin/env python3
import finalcode2
import re
import json

def add_table_title_tags(table_html, converted_json, matrix, classification):
    """
    Add table title tags similar to caption tags for tables like Table 6.
    
    This function checks if a table should have a title tag added and wraps
    the converted JSON with title information, similar to how captions are handled.
    
    Args:
        table_html: Original table HTML
        converted_json: The converted JSON string
        matrix: Table matrix data
        classification: Table classification
        
    Returns:
        str: JSON with title tags if applicable, otherwise original JSON
    """
    # Check if this table should have a title tag
    if (matrix and len(matrix) > 0 and 
        classification == "wide_column_grouped_layout" and
        finalcode2.should_preserve_table_header(matrix, classification)):
        
        # Extract table title (first cell of Row 0)
        table_title = matrix[0][0].strip() if matrix[0] and matrix[0][0].strip() else None
        
        if table_title:
            # Add title tag with just the actual title (clean format)
            title_tag = f"<title>{table_title}</title>"
            return f"{title_tag}\n{converted_json}"
    
    return converted_json

def process_table_with_title_tags(table_html):
    """
    Process a single table and add title tags if needed.
    
    This demonstrates how to integrate title tag addition into the 
    existing table processing workflow.
    """
    # Process the table normally
    processed_tables = finalcode2.process_tables(table_html)
    if not processed_tables:
        return None
        
    table_data = processed_tables[0]
    
    # Get classification and matrix
    classification = finalcode2.classify_table_from_data(table_data)
    matrix = finalcode2.build_expanded_matrix(table_data)
    
    # Convert to JSON (using original conversion, not the wrapper)
    if classification == "wide_column_grouped_layout":
        # Use original conversion function directly
        if finalcode2.is_simple_colspan_table(matrix):
            result = finalcode2.convert_standard_table(matrix)
        else:
            result = finalcode2.convert_temporal_grouped_table(matrix)
    else:
        result = finalcode2.convert_to_key_value_json(table_data, classification)
    
    # Convert to JSON string
    json_str = json.dumps(result, ensure_ascii=False)
    
    # Add title tags if needed
    final_output = add_table_title_tags(table_html, json_str, matrix, classification)
    
    return final_output

# Example usage
if __name__ == "__main__":
    print('=== EXAMPLE: TABLE TITLE TAGS LIKE CAPTION TAGS ===')
    print()
    
    # Load and test with Table 6
    with open('di_parser_output.txt', 'r', encoding='utf-8') as f:
        content = f.read()
    
    tables = re.findall(r'<table>.*?</table>', content, re.DOTALL)
    
    for table in tables:
        if 'Table 6:' in table:
            print('=== TABLE 6 EXAMPLE ===')
            print()
            
            # Show current format (with wrapper)
            print('Current format (with wrapper):')
            processed = finalcode2.process_tables(table)
            if processed:
                result = finalcode2.convert_to_key_value_json(processed[0], 
                    finalcode2.classify_table_from_data(processed[0]))
                print(json.dumps(result, indent=2))
            print()
            
            # Show proposed format (with title tags)
            print('Proposed format (with title tags):')
            title_tagged_output = process_table_with_title_tags(table)
            print(title_tagged_output)
            print()
            
            break
    
    # Test with a regular table (should not have title tags)
    print('=== REGULAR TABLE EXAMPLE (should not have title tags) ===')
    regular_table = '<table><tr><th>Name</th><th>Age</th></tr><tr><td>John</td><td>25</td></tr></table>'
    regular_output = process_table_with_title_tags(regular_table)
    print(regular_output)
    print()
    
    print('=== SUMMARY ===')
    print('✅ Table 6: Gets <title> tag added')
    print('✅ Regular tables: No title tag added')
    print('✅ Similar to caption tag approach')
    print('✅ Title not mixed into JSON structure')
