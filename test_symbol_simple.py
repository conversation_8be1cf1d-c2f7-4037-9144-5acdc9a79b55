#!/usr/bin/env python3
"""
Simple test for symbol detection and replacement.
"""

import finalcode2

def test_symbol_detection():
    """Test symbol detection directly"""
    
    print("=== TESTING SYMBOL DETECTION ===\n")
    
    # Test direct symbol detection
    test_text = "☒ ☐ ✓ ✗"
    print(f"Test text: {test_text}")
    
    symbols = finalcode2.detect_unicode_symbols(test_text)
    print(f"Detected symbols: {symbols}")
    
    # Test symbol replacement
    replaced = finalcode2.replace_symbols(test_text)
    print(f"Replaced text: {replaced}")
    print()

def test_encoding_scenarios():
    """Test different encoding scenarios"""
    
    print("=== TESTING ENCODING SCENARIOS ===\n")
    
    # Scenario 1: Normal UTF-8
    normal_text = "Status: ☒ Complete"
    print(f"Normal UTF-8: {normal_text}")
    print(f"Replaced: {finalcode2.replace_symbols(normal_text)}")
    print()
    
    # Scenario 2: Unicode escape sequences
    escaped_text = "Status: \\u2612 Complete"
    print(f"Escaped Unicode: {escaped_text}")
    print(f"Replaced: {finalcode2.replace_symbols(escaped_text)}")
    print()
    
    # Scenario 3: Unicode code points
    code_text = "Status: U+2612 Complete"
    print(f"Unicode codes: {code_text}")
    print(f"Replaced: {finalcode2.replace_symbols(code_text)}")
    print()

def test_file_processing():
    """Test processing the actual test_symbol.txt file"""
    
    print("=== TESTING FILE PROCESSING ===\n")
    
    try:
        # Read file with different encodings
        encodings = ['utf-8', 'latin-1', 'cp1252']
        
        for encoding in encodings:
            try:
                with open('test/test_symbol.txt', 'r', encoding=encoding) as f:
                    content = f.read()
                print(f"✅ Successfully read with {encoding}")
                
                # Check for symbols
                symbol_count = sum(1 for char in content if char in finalcode2.ALL_SYMBOLS)
                print(f"   Symbols found: {symbol_count}")
                
                if symbol_count > 0:
                    print(f"   Using {encoding} encoding")
                    break
                    
            except Exception as e:
                print(f"❌ Failed with {encoding}: {e}")
        
        # Test the processing
        print("\nTesting processing...")
        
        # Use a minimal test case
        minimal_test = "{'content': '<table><tr><th>Test</th></tr><tr><td>☒</td></tr></table>', 'language': 'en'}"
        
        print(f"Input: {minimal_test}")
        
        # Test just the symbol replacement part
        replaced = finalcode2.replace_symbols(minimal_test)
        print(f"After symbol replacement: {replaced}")
        
    except Exception as e:
        print(f"Error in file processing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_symbol_detection()
    test_encoding_scenarios()
    test_file_processing()
