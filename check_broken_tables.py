#!/usr/bin/env python3
import finalcode2
import re

# Check what happened to tables 2, 20, and 21
with open('di_parser_output.txt', 'r', encoding='utf-8') as f:
    content = f.read()

tables = re.findall(r'<table>.*?</table>', content, re.DOTALL)

print('=== CHECKING TABLES 2, 20, 21 FROM DIPARSER ===')
print()

target_tables = ['Table 2:', 'Table 20:', 'Table 21:']

for table in tables:
    for target in target_tables:
        if target in table:
            table_num = target.replace('Table ', '').replace(':', '')
            print(f'=== TABLE {table_num} ===')
            
            # Process it
            processed_tables = finalcode2.process_tables(table)
            if processed_tables:
                table_data = processed_tables[0]
                
                # Get classification and matrix
                classification = finalcode2.classify_table_from_data(table_data)
                matrix = finalcode2.build_expanded_matrix(table_data)
                
                print(f'Classification: {classification}')
                print('Row 0:', matrix[0] if matrix else 'None')
                
                # Check if header preservation is triggered
                should_preserve = finalcode2.should_preserve_table_header(matrix, classification)
                print(f'Should preserve header: {should_preserve}')
                
                # Show result
                result = finalcode2.convert_to_key_value_json(table_data, classification)
                print(f'Result type: {type(result)}')
                
                if isinstance(result, dict) and 'table_header' in result:
                    print('❌ PROBLEM: Table has table_header (should not!)')
                    print(f'   Header: {result["table_header"]}')
                else:
                    print('✅ OK: Table uses original format')
                    
                print()
            break
