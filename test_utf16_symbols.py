#!/usr/bin/env python3
"""
Test UTF-16 encoding handling for symbol detection and replacement.
"""

import finalcode2
import tempfile
import os

def create_utf16_test_file():
    """Create a test file with UTF-16 encoding containing symbols"""
    
    # Test content with symbols
    test_content = {
        'content': '<table><tr><th>Status</th><th>Result</th></tr><tr><td>Complete</td><td>☒</td></tr><tr><td>Pending</td><td>☐</td></tr></table>',
        'language': 'en'
    }
    
    content_str = str(test_content)
    
    # Create temporary files with different UTF-16 encodings
    test_files = {}
    
    encodings = [
        ('utf-16', 'UTF-16 with BOM'),
        ('utf-16le', 'UTF-16 Little Endian'),
        ('utf-16be', 'UTF-16 Big Endian'),
        ('utf-8', 'UTF-8 (for comparison)')
    ]
    
    for encoding, description in encodings:
        try:
            # Create temporary file
            fd, filepath = tempfile.mkstemp(suffix=f'_{encoding}.txt', text=True)
            
            with os.fdopen(fd, 'w', encoding=encoding) as f:
                f.write(content_str)
            
            test_files[encoding] = {
                'path': filepath,
                'description': description
            }
            
        except Exception as e:
            print(f"Failed to create {encoding} file: {e}")
    
    return test_files

def test_utf16_detection():
    """Test UTF-16 file detection and processing"""
    
    print("=== TESTING UTF-16 SYMBOL DETECTION ===\n")
    
    # Create test files
    test_files = create_utf16_test_file()
    
    try:
        for encoding, file_info in test_files.items():
            filepath = file_info['path']
            description = file_info['description']
            
            print(f"Testing {description}:")
            
            # Test encoding detection
            detected_encoding = finalcode2.detect_file_encoding(filepath)
            print(f"  Detected encoding: {detected_encoding}")
            
            # Test file reading with different methods
            try:
                # Method 1: Let Python auto-detect
                with open(filepath, 'r', encoding=detected_encoding) as f:
                    content = f.read()
                print(f"  ✅ Successfully read with detected encoding")
                
                # Count symbols in content
                symbol_count = sum(1 for char in content if char in finalcode2.ALL_SYMBOLS)
                print(f"  Symbols found: {symbol_count}")
                
                # Test symbol detection
                symbols = finalcode2.detect_unicode_symbols(content)
                print(f"  Detected symbols: {len(symbols)}")
                
                # Test symbol replacement
                replaced = finalcode2.replace_symbols(content)
                has_yes = 'Yes' in replaced
                has_no = 'No' in replaced
                print(f"  Symbol replacement: {'✅' if has_yes and has_no else '❌'}")
                
                # Test full processing
                try:
                    result = finalcode2.correct_tables(content)
                    if result and ('Yes' in str(result) or 'No' in str(result)):
                        print(f"  Full processing: ✅")
                    else:
                        print(f"  Full processing: ❌")
                except Exception as e:
                    print(f"  Full processing: ❌ ({e})")
                
            except Exception as e:
                print(f"  ❌ Failed to read: {e}")
            
            print()
    
    finally:
        # Clean up temporary files
        for file_info in test_files.values():
            try:
                os.unlink(file_info['path'])
            except Exception:
                pass

def test_utf16_edge_cases():
    """Test UTF-16 specific edge cases"""
    
    print("=== TESTING UTF-16 EDGE CASES ===\n")
    
    # Test 1: BOM handling
    print("Test 1: BOM handling")
    bom_text = '\ufeff☒ Complete'
    cleaned = finalcode2.handle_encoding_issues(bom_text)
    print(f"  Original: {repr(bom_text)}")
    print(f"  Cleaned: {repr(cleaned)}")
    print(f"  BOM removed: {'✅' if not cleaned.startswith('\ufeff') else '❌'}")
    print()
    
    # Test 2: Unicode escape sequences
    print("Test 2: Unicode escape sequences")
    escaped_text = 'Status: \\u2612 Complete'
    replaced = finalcode2.replace_symbols(escaped_text)
    print(f"  Original: {escaped_text}")
    print(f"  Replaced: {replaced}")
    print(f"  Escape handled: {'✅' if 'Yes' in replaced else '❌'}")
    print()
    
    # Test 3: Unicode code points
    print("Test 3: Unicode code points")
    code_text = 'Status: U+2612 Complete'
    replaced = finalcode2.replace_symbols(code_text)
    print(f"  Original: {code_text}")
    print(f"  Replaced: {replaced}")
    print(f"  Code point handled: {'✅' if 'Yes' in replaced else '❌'}")
    print()
    
    # Test 4: Mixed encoding scenarios
    print("Test 4: Mixed encoding scenarios")
    mixed_text = 'Status: ☒ U+2610 \\u2713'
    replaced = finalcode2.replace_symbols(mixed_text)
    print(f"  Original: {mixed_text}")
    print(f"  Replaced: {replaced}")
    yes_count = replaced.count('Yes')
    no_count = replaced.count('No')
    print(f"  All symbols replaced: {'✅' if yes_count >= 2 and no_count >= 1 else '❌'}")

def test_encoding_robustness():
    """Test robustness across different encoding scenarios"""
    
    print("\n=== TESTING ENCODING ROBUSTNESS ===\n")
    
    # Test various problematic scenarios
    test_cases = [
        ('Normal UTF-8', '☒ Complete ☐ Pending'),
        ('With BOM', '\ufeff☒ Complete ☐ Pending'),
        ('Unicode escapes', '\\u2612 Complete \\u2610 Pending'),
        ('Code points', 'U+2612 Complete U+2610 Pending'),
        ('Mixed format', '☒ U+2610 \\u2713 Complete'),
    ]
    
    for test_name, test_text in test_cases:
        print(f"Testing {test_name}:")
        print(f"  Input: {repr(test_text)}")
        
        try:
            # Test detection
            symbols = finalcode2.detect_unicode_symbols(test_text)
            print(f"  Symbols detected: {len(symbols)}")
            
            # Test replacement
            replaced = finalcode2.replace_symbols(test_text)
            print(f"  Output: {replaced}")
            
            # Check success
            has_replacements = any(word in replaced for word in ['Yes', 'No', 'Unknown'])
            print(f"  Success: {'✅' if has_replacements else '❌'}")
            
        except Exception as e:
            print(f"  Error: ❌ {e}")
        
        print()

if __name__ == "__main__":
    test_utf16_detection()
    test_utf16_edge_cases()
    test_encoding_robustness()
    
    print("=== SUMMARY ===")
    print("✅ Enhanced symbol detection now handles:")
    print("   - UTF-16 with BOM detection")
    print("   - UTF-16LE and UTF-16BE")
    print("   - Unicode escape sequences (\\uXXXX)")
    print("   - Unicode code points (U+XXXX)")
    print("   - Mixed encoding scenarios")
    print("   - BOM removal and normalization")
    print("   - Fallback encoding detection")
