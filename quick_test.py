#!/usr/bin/env python3

import sys
sys.path.append('.')

def quick_test():
    try:
        from finalcode2 import load_table_data, classify_table_from_data, convert_to_key_value_json
        
        data = load_table_data('di_parser_output.txt')
        tables = data.get('content', {}).get('tables', [])
        
        # Test Table 6
        if len(tables) > 5:
            table_6 = tables[5]
            print(f"Table 6 caption: {table_6.get('caption', 'N/A')}")
            
            classification = classify_table_from_data(table_6)
            print(f"Table 6 classification: {classification}")
            
            result = convert_to_key_value_json(table_6, classification)
            print(f"Table 6 result type: {type(result)}")
            if isinstance(result, dict) and len(result) > 0:
                print("✅ Table 6 FIXED - Nested dictionary")
                print(f"Keys: {list(result.keys())}")
            else:
                print("❌ Table 6 STILL BROKEN - Flat list")
        
        # Test Table 29
        if len(tables) > 28:
            table_29 = tables[28]
            print(f"\nTable 29 caption: {table_29.get('caption', 'N/A')}")
            
            classification = classify_table_from_data(table_29)
            print(f"Table 29 classification: {classification}")
            
            result = convert_to_key_value_json(table_29, classification)
            print(f"Table 29 result type: {type(result)}")
            if isinstance(result, dict) and len(result) > 0:
                print("✅ Table 29 FIXED - Nested dictionary")
                print(f"Keys: {list(result.keys())}")
            else:
                print("❌ Table 29 STILL BROKEN - Flat list")
                
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_test()
