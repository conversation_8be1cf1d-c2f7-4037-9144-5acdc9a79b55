#!/usr/bin/env python3
"""
Test script to check if finalcode2.py properly handles tables with empty rows.
Tests a table with 7 rows where 2 rows are totally empty.
"""

import finalcode2

def test_empty_rows():
    """Test table with 7 rows, 2 of which are completely empty"""
    
    # Create HTML table with 7 rows, 2 completely empty
    html_content = """
    <table>
        <caption>Test Table with Empty Rows</caption>
        <tr>
            <th>Name</th>
            <th>Age</th>
            <th>City</th>
        </tr>
        <tr>
            <td>John</td>
            <td>25</td>
            <td>New York</td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td>Mary</td>
            <td>30</td>
            <td>London</td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td>Bob</td>
            <td>35</td>
            <td>Paris</td>
        </tr>
        <tr>
            <td>Alice</td>
            <td>28</td>
            <td>Tokyo</td>
        </tr>
    </table>
    """
    
    print("=== TESTING EMPTY ROWS HANDLING ===")
    print("Original table has 7 rows (including header):")
    print("- Row 1: Header (Name, Age, City)")
    print("- Row 2: John, 25, New York")
    print("- Row 3: EMPTY (all cells empty)")
    print("- Row 4: Mary, 30, London") 
    print("- Row 5: EMPTY (all cells empty)")
    print("- Row 6: Bob, 35, Paris")
    print("- Row 7: Alice, 28, Tokyo")
    print()
    
    # Process the table
    result = finalcode2.correct_tables(html_content)
    
    print("=== PROCESSED RESULT ===")
    print(result)
    print()
    
    # Check if empty rows are excluded
    import json
    # Extract JSON from the result
    start_idx = result.find('[')
    end_idx = result.rfind(']') + 1
    if start_idx != -1 and end_idx != -1:
        json_str = result[start_idx:end_idx]
        try:
            parsed_data = json.loads(json_str)
            print(f"=== ANALYSIS ===")
            print(f"Number of data rows in output: {len(parsed_data)}")
            print(f"Expected: 4 rows (John, Mary, Bob, Alice)")
            print(f"Empty rows should be excluded: {'✓ PASS' if len(parsed_data) == 4 else '✗ FAIL'}")
            print()
            
            # Show each row
            for i, row in enumerate(parsed_data, 1):
                print(f"Row {i}: {row}")
                
        except json.JSONDecodeError:
            print("Could not parse JSON from result")
    else:
        print("Could not extract JSON from result")

if __name__ == "__main__":
    test_empty_rows()
