#!/usr/bin/env python3
import finalcode2
import re

# Test the new title tag implementation
with open('di_parser_output.txt', 'r', encoding='utf-8') as f:
    content = f.read()

tables = re.findall(r'<table>.*?</table>', content, re.DOTALL)

print('=== TESTING TITLE TAG IMPLEMENTATION ===')
print()

# Test Table 6 (should get title tag)
print('1. TABLE 6 TEST (should get title tag)')
for table in tables:
    if 'Table 6:' in table:
        # Process with the new title tag logic
        result = finalcode2.correct_tables(table)
        print('Result:')
        print(result)
        
        if '<title>' in result:
            # Extract title content
            title_match = re.search(r'<title>(.*?)</title>', result)
            if title_match:
                title_content = title_match.group(1)
                print(f'✅ SUCCESS: Title tag found: "{title_content}"')
            else:
                print('❌ ISSUE: Title tag malformed')
        else:
            print('❌ ISSUE: No title tag found')
        print()
        break

# Test Table 29 (should NOT get title tag)
print('2. TABLE 29 TEST (should NOT get title tag)')
for table in tables:
    if 'Table 29:' in table:
        result = finalcode2.correct_tables(table)
        
        if '<title>' not in result:
            print('✅ SUCCESS: No title tag (as expected)')
        else:
            print('❌ ISSUE: Title tag found (should not have one)')
        print()
        break

# Test regular table (should NOT get title tag)
print('3. REGULAR TABLE TEST (should NOT get title tag)')
regular_table = '<table><tr><th>Name</th><th>Age</th></tr><tr><td>John</td><td>25</td></tr></table>'
result = finalcode2.correct_tables(regular_table)

if '<title>' not in result:
    print('✅ SUCCESS: No title tag (as expected)')
else:
    print('❌ ISSUE: Title tag found (should not have one)')

print()
print('=== SUMMARY ===')
print('Title tags should only appear for Table 6 type tables')
print('Format: <title>Actual Title</title> (no extra phrase)')
