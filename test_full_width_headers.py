#!/usr/bin/env python3
"""
Test script for tables with full-width headers (th elements with colspan equal to number of columns).

This script tests the new functionality where:
1. Tables with th elements having colspan equal to total columns are detected
2. Such tables are treated as having no header row
3. The spanning header text is wrapped in <header> tags in the output
4. The table data is converted with generic column names (column_1, column_2, etc.)
"""

import finalcode2

def test_full_width_header_tables():
    """Test 5 different tables with full-width headers"""
    
    print("=== TESTING TABLES WITH FULL-WIDTH HEADERS ===\n")
    
    # Test 1: Simple table with full-width header
    test1_html = '''
    <table>
    <caption>Test Table 1</caption>
    <tr><th colspan="3">Sales Report Q1 2024</th></tr>
    <tr><td>Product A</td><td>100</td><td>$5000</td></tr>
    <tr><td>Product B</td><td>150</td><td>$7500</td></tr>
    <tr><td>Product C</td><td>200</td><td>$10000</td></tr>
    </table>
    '''
    
    # Test 2: Table with full-width header and 4 columns
    test2_html = '''
    <table>
    <caption>Test Table 2</caption>
    <tr><th colspan="4">Employee Performance Review</th></tr>
    <tr><td>John Smith</td><td>Manager</td><td>Excellent</td><td>95%</td></tr>
    <tr><td>Jane Doe</td><td>Developer</td><td>Good</td><td>88%</td></tr>
    <tr><td>Bob Wilson</td><td>Analyst</td><td>Average</td><td>75%</td></tr>
    </table>
    '''
    
    # Test 3: Table with full-width header and 5 columns
    test3_html = '''
    <table>
    <tr><th colspan="5">Monthly Budget Summary</th></tr>
    <tr><td>January</td><td>$1000</td><td>$800</td><td>$200</td><td>20%</td></tr>
    <tr><td>February</td><td>$1200</td><td>$900</td><td>$300</td><td>25%</td></tr>
    <tr><td>March</td><td>$1100</td><td>$850</td><td>$250</td><td>23%</td></tr>
    </table>
    '''
    
    # Test 4: Table with full-width header and mixed data types
    test4_html = '''
    <table>
    <caption>Test Table 4</caption>
    <tr><th colspan="6">Project Status Dashboard</th></tr>
    <tr><td>Project Alpha</td><td>In Progress</td><td>75%</td><td>2024-03-15</td><td>High</td><td>$50000</td></tr>
    <tr><td>Project Beta</td><td>Completed</td><td>100%</td><td>2024-02-28</td><td>Medium</td><td>$30000</td></tr>
    <tr><td>Project Gamma</td><td>Planning</td><td>10%</td><td>2024-04-01</td><td>Low</td><td>$20000</td></tr>
    </table>
    '''
    
    # Test 5: Table with full-width header and 2 columns (minimal case)
    test5_html = '''
    <table>
    <caption>Test Table 5</caption>
    <tr><th colspan="2">Simple Key-Value Pairs</th></tr>
    <tr><td>Name</td><td>John Doe</td></tr>
    <tr><td>Age</td><td>30</td></tr>
    <tr><td>City</td><td>New York</td></tr>
    <tr><td>Country</td><td>USA</td></tr>
    </table>
    '''
    
    test_cases = [
        ("Test 1: 3-column table with full-width header", test1_html),
        ("Test 2: 4-column table with full-width header", test2_html),
        ("Test 3: 5-column table with full-width header (no caption)", test3_html),
        ("Test 4: 6-column table with full-width header", test4_html),
        ("Test 5: 2-column table with full-width header", test5_html)
    ]
    
    for test_name, html_content in test_cases:
        print(f"--- {test_name} ---")
        print(f"Input HTML:")
        print(html_content.strip())
        print()
        
        # Process the table
        try:
            result = finalcode2.correct_tables(html_content)
            print(f"Output:")
            print(result.strip())
            print()
            
            # Parse and analyze the result
            if "<header>" in result and "</header>" in result:
                header_start = result.find("<header>") + 8
                header_end = result.find("</header>")
                header_text = result[header_start:header_end]
                print(f"✅ Full-width header detected: '{header_text}'")
            else:
                print("❌ No <header> tags found in output")
                
            if '"column_1"' in result:
                print("✅ Generic column names used (column_1, column_2, etc.)")
            else:
                print("❌ Generic column names not found")
                
        except Exception as e:
            print(f"❌ Error processing table: {e}")
        
        print("=" * 60)
        print()

if __name__ == "__main__":
    test_full_width_header_tables()
