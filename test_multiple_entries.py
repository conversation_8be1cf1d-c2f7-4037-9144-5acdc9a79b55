#!/usr/bin/env python3
"""
Test the multiple entries fix functionality.
"""

import finalcode2

def test_multiple_entries_fix():
    """Test fixing tables with multiple entries parsed as separate td tags"""
    
    print("=== TESTING MULTIPLE ENTRIES FIX ===\n")
    
    # Create test case with the problem you described
    test_html = '''
    <table>
    <caption>Test Multiple Entries</caption>
    <tr><th>data</th><th>year</th></tr>
    <tr><td>windows</td><td>linux</td><td>2010</td><td>2011</td></tr>
    </table>
    '''
    
    print("Original problematic HTML:")
    print(test_html)
    print()
    
    # Test the fix function directly
    print("Testing fix_excess_td_tags function:")
    fixed_html = finalcode2.fix_excess_td_tags(test_html)
    print("Fixed HTML:")
    print(fixed_html)
    print()
    
    # Test the full processing pipeline
    print("Testing full processing pipeline:")
    result = finalcode2.correct_tables(test_html)
    print("Final result:")
    print(result)
    print()
    
    # Check if the fix worked
    if 'windows, linux' in result and '2010, 2011' in result:
        print("✅ SUCCESS: Multiple entries correctly merged!")
    else:
        print("❌ ISSUE: Multiple entries not properly merged")
        print("Expected: 'windows, linux' and '2010, 2011'")

def test_normal_table():
    """Test that normal tables still work correctly"""
    
    print("=== TESTING NORMAL TABLE COMPATIBILITY ===\n")
    
    normal_html = '''
    <table>
    <caption>Normal Table</caption>
    <tr><th>Product</th><th>Price</th></tr>
    <tr><td>Laptop</td><td>$999</td></tr>
    <tr><td>Mouse</td><td>$29</td></tr>
    </table>
    '''
    
    result = finalcode2.correct_tables(normal_html)
    print("Normal table result:")
    print(result)
    
    if '"Product":"Laptop"' in result and '"Price":"$999"' in result:
        print("✅ SUCCESS: Normal tables still work correctly!")
    else:
        print("❌ ISSUE: Normal table processing broken")

if __name__ == "__main__":
    test_multiple_entries_fix()
    test_normal_table()
