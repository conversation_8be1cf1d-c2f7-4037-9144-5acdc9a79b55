#!/usr/bin/env python3
import finalcode2
import json
import re

# Test trial26.txt current behavior
with open('test/trial26.txt', 'r', encoding='utf-8') as f:
    content = f.read()

print('Testing trial26.txt current behavior...')
print()

# Parse the content
data = json.loads(content)
html_content = data['content']

# Extract first table
first_table_match = re.search(r'<table>.*?</table>', html_content, re.DOTALL)
if first_table_match:
    first_table = first_table_match.group(0)
    print('First table HTML:')
    print(first_table)
    print()
    
    # Process it
    tables = finalcode2.process_tables(first_table)
    if tables:
        table = tables[0]
        print('Raw structure:')
        for i, row in enumerate(table['with_span']):
            row_texts = [cell['text'] for cell in row]
            print(f'Row {i}: {row_texts}')
        print()
        
        # Build matrix
        matrix = finalcode2.build_expanded_matrix(table)
        print('Matrix:')
        for i, row in enumerate(matrix):
            print(f'Row {i}: {row}')
        print()
        
        # Classify and convert
        classification = finalcode2.classify_table_from_data(table)
        print(f'Classification: {classification}')
        
        result = finalcode2.convert_to_key_value_json(table, classification)
        print(f'Result: {result}')
        print()
        
        print('Expected result should be:')
        print('[{"Product": "Product A", "Revenue": "5000", "Growth": "12%"}, {"Product": "Product B", "Revenue": "3000", "Growth": "8%"}]')
