#!/usr/bin/env python3

import sys
sys.path.append('.')

# Test Table 6 and 29 specifically
def debug_table_6_and_29():
    print("=== DEBUGGING TABLE 6 AND 29 IN FINALCODE2 ===")

    try:
        from finalcode2 import load_table_data, extract_table_features, classify_table_from_data, build_expanded_matrix, convert_to_key_value_json, is_year_metric_region_table

        data = load_table_data('di_parser_output.txt')
        tables = data.get('content', {}).get('tables', [])

        # Test Table 6
        if len(tables) > 5:
            table_6 = tables[5]  # Table 6 is at index 5
            print("\n--- TABLE 6 ---")

            classification = classify_table_from_data(table_6)
            print(f"Classification: {classification}")

            matrix = build_expanded_matrix(table_6)
            print(f"Matrix preview (first 3 rows):")
            for i, row in enumerate(matrix[:3]):
                print(f"  Row {i}: {row}")

            # Test the detection function
            is_year_metric = is_year_metric_region_table(matrix)
            print(f"Is year-metric-region table: {is_year_metric}")

            features = extract_table_features(table_6)
            print(f"Empty ratio: {features.get('empty_ratio', 'N/A')}")

            result = convert_to_key_value_json(table_6, classification)
            print(f"Result type: {type(result)}")
            print(f"Result preview: {str(result)[:200]}...")

        # Test Table 29
        if len(tables) > 28:
            table_29 = tables[28]  # Table 29 is at index 28
            print("\n--- TABLE 29 ---")

            classification = classify_table_from_data(table_29)
            print(f"Classification: {classification}")

            matrix = build_expanded_matrix(table_29)
            print(f"Matrix preview (first 3 rows):")
            for i, row in enumerate(matrix[:3]):
                print(f"  Row {i}: {row}")

            # Test the detection function
            is_year_metric = is_year_metric_region_table(matrix)
            print(f"Is year-metric-region table: {is_year_metric}")

            features = extract_table_features(table_29)
            print(f"Empty ratio: {features.get('empty_ratio', 'N/A')}")

            result = convert_to_key_value_json(table_29, classification)
            print(f"Result type: {type(result)}")
            print(f"Result preview: {str(result)[:200]}...")

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_table_6_and_29()
