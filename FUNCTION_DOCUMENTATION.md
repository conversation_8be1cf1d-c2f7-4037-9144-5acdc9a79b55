# Table Processing System - Complete Function Documentation

## Overview

This document provides comprehensive documentation for the table processing and conversion system implemented in `finalcode2.py`. The system converts HTML tables with complex structures (colspan, rowspan, hierarchical headers) to JSON format.

## Core Architecture

### Main Processing Flow
```
HTML Input → correct_tables() → process_tables() → classify_table_from_data() → convert_to_key_value_json() → combine_suffix_columns() → JSON Output
```

## Function Categories

### 1. Core Processing Functions

#### `correct_tables(input_string: str) → str`
**Main entry point** for table processing with individual table workflow.
- **Input**: HTML string or nested dictionary with 'content' key
- **Output**: Processed string with tables converted to JSON
- **Features**: Sequential processing, structure preservation, error handling
- **File I/O**: Saves output to 'all_tables_converted.txt'

#### `process_tables(html_content: str) → List[Dict[str, Any]]`
**Table extraction** with fallback parsing strategy (lxml → BeautifulSoup).
- **Input**: HTML string containing table elements
- **Output**: List of table dictionaries with metadata and structure
- **Features**: Caption extraction, colspan/rowspan handling, position tracking

#### `classify_table_from_data(table_data: Dict[str, Any]) → str`
**Structure classification** for conversion strategy selection.
- **Input**: Table data dictionary with 'with_span' structure
- **Output**: Classification string (alignment_based, complex_header_spanning, etc.)
- **Features**: Pattern analysis, empty ratio calculation, feature extraction

#### `convert_to_key_value_json(table: Dict[str, Any], classification: str) → Union[Dict, List]`
**Main conversion dispatcher** routing to specialized conversion functions.
- **Input**: Table data and classification type
- **Output**: JSON structure (Dict or List based on classification)
- **Features**: Matrix expansion, special pattern detection, fallback handling

#### `combine_suffix_columns(data: Any) → Any`
**Column suffix combining** for _2, _3, _4 patterns with comma separation.
- **Input**: JSON data structure (any type)
- **Output**: Modified structure with suffix columns combined
- **Features**: Recursive processing, comma separation, empty value handling

### 2. Table Extraction Functions

#### `process_tables_with_lxml(html_content) → List[Dict[str, Any]]`
Primary HTML parsing using lxml for performance.

#### `process_tables_with_bs4(html_content) → List[Dict[str, Any]]`
Fallback HTML parsing using BeautifulSoup for malformed HTML.

#### `extract_table_with_span_lxml(table_el) → List[List[Dict]]`
Extract table structure with colspan/rowspan using lxml.

#### `extract_table_with_span_bs4(table) → List[List[Dict]]`
Extract table structure with colspan/rowspan using BeautifulSoup.

### 3. Structure Analysis Functions

#### `extract_table_features(table: Dict[str, Any]) → Dict[str, Any]`
Extract structural features for classification.

#### `build_expanded_matrix(table: Dict[str, Any]) → List[List[str]]`
Build expanded matrix handling colspan/rowspan.

#### `classify_by_structure(features: Dict[str, Any]) → str`
Apply classification rules based on extracted features.

### 4. Conversion Functions by Table Type

#### Alignment-Based Tables
- `convert_alignment_based_table(matrix) → Dict[str, Any]`

#### Header Spanning Tables
- `convert_complex_header_spanning_table(matrix) → List[Dict[str, str]]`
- `convert_filled_colspan_header_spanning_table(matrix) → Dict[str, Dict[str, str]]`

#### Grouped and Hierarchical Tables
- `convert_grouped_header_table(matrix) → List[Dict[str, str]]`
- `convert_hierarchical_two_level_table(matrix) → Dict[str, Any]`
- `convert_rowspan_pattern_table(matrix) → Dict[str, Dict[str, str]]`

#### Standard Tables
- `convert_simple_table(matrix) → Union[Dict, List]`
- `convert_standard_table(matrix) → List[Dict[str, str]]`
- `convert_table_with_empty_columns(matrix) → List[Dict[str, str]]`

### 5. Symbol Processing Functions

#### `detect_unicode_symbols(text: str) → List[Dict[str, str]]`
Detect Unicode symbols in text content.

#### `get_symbol_category(symbol: str) → str`
Categorize Unicode symbols (checkbox, checkmark, cross, neutral).

#### `get_replacement_for_symbol(symbol: str, context: str) → str`
Get context-aware replacement for symbols.

#### `detect_context_from_text(text: str) → str`
Detect context from surrounding text.

#### `process_json_data_with_symbol_replacement(data, context_map, path) → Any`
Apply symbol replacement to JSON data structures.

### 6. Utility Functions

#### `load_table_data(filepath: str) → Dict[str, Any]`
Load table data from file.

#### `classify_table_file(filepath: str) → str`
Classify table structure from file.

#### `remove_whitespace_and_blank_lines(text: str) → str`
Clean whitespace for single-line output.

## Classification System

### Table Types
- **alignment_based**: High empty ratio tables (>40%) for alignment
- **complex_header_spanning**: Multi-level headers with colspan/rowspan
- **filled_colspan_header_spanning**: Headers with colspan, all cells filled
- **grouped_header**: Tables with grouped column headers
- **hierarchical_two_level**: Nested structures with rowspan grouping
- **simple**: Standard tabular data
- **other**: Fallback for unclassified structures

### Classification Features
- Empty cell ratio analysis
- Colspan/rowspan pattern detection
- Header structure analysis
- Data distribution patterns
- Meaningful column detection

## Input/Output Formats

### Input
- HTML string containing `<table>` elements
- Nested dictionary with 'content' key (di_parser_output.txt format)
- Supports multiple tables with various structures

### Output
- HTML string with tables converted to JSON
- Preserves captions and non-table content
- Nested dictionaries for hierarchical data
- List of dictionaries for tabular data
- Key-value pairs for simple structures

## Testing System

### Test Files
- **test.py**: Basic testing (di_parser + trial1-2)
- **test2.py**: Comprehensive testing with console output
- **comprehensive_test.py**: Comprehensive testing with file output

### Test Coverage
- 24 files processed
- 240 tables converted
- 100% success rate
- All table types supported

## Complete Function Inventory

### Pattern Detection Functions
- `is_rowspan_pattern_table(matrix) → bool`: Detect rowspan grouping patterns
- `has_suffix_columns(data) → bool`: Check for _2, _3, _4 suffix patterns
- `is_meaningful_column(matrix, col_idx) → bool`: Detect columns with actual data

### Special Case Converters
- `convert_single_row_table(matrix) → Dict`: Handle single-row tables
- `convert_table_with_section_headers(matrix) → Dict`: Process sectioned data
- `convert_temporal_sectioned_table(matrix) → Dict`: Handle time-based sections

### Matrix Processing
- `get_meaningful_columns(matrix) → List[int]`: Identify data-containing columns
- `detect_section_headers(matrix) → List[int]`: Find section header rows
- `expand_cell_with_span(cell, row_idx, col_idx) → List`: Handle spanning cells

### Context and Symbol Processing
- `get_context_aware_replacement(symbol, context) → str`: Smart symbol replacement
- `apply_symbol_replacement_strategy(data, strategy) → Any`: Apply replacement rules
- `normalize_symbol_text(text) → str`: Standardize symbol representations

### File I/O and Utilities
- `save_output_file(content, filename) → None`: Save processed content
- `validate_table_structure(table_data) → bool`: Verify table integrity
- `clean_cell_text(text) → str`: Normalize cell content

## Processing Workflow Details

### 1. Input Processing
```
Input String → Parse Dictionary Structure → Extract HTML Content → Identify Tables
```

### 2. Individual Table Processing
```
Table HTML → Parse Structure → Extract Features → Classify Type → Convert to JSON → Apply Suffix Combining → Replace in Content
```

### 3. Output Generation
```
Processed Content → Remove Whitespace → Save to File → Return Result
```

## Error Handling

### Parsing Fallbacks
- lxml parsing failure → BeautifulSoup fallback
- BeautifulSoup failure → Skip table with warning
- Invalid table structure → Return empty result

### Classification Fallbacks
- Unrecognized pattern → "other" classification
- Feature extraction failure → "simple" classification
- Empty table → Return empty dict

### Conversion Fallbacks
- Conversion function failure → Try alternative converter
- Matrix expansion failure → Use raw table data
- JSON serialization failure → Return string representation

## Performance Characteristics

### Processing Speed
- lxml parsing: Fast for well-formed HTML
- BeautifulSoup fallback: Slower but more robust
- Matrix expansion: O(rows × cols × max_span)
- Classification: O(cells) for feature extraction

### Memory Usage
- In-memory processing: No temporary files
- Matrix expansion: Proportional to table size
- Recursive processing: Stack depth = nesting level

## Usage Examples

```python
# Basic usage
html_input = '<table><caption>Sales</caption><tr><th>Region</th><th>Q1</th></tr><tr><td>North</td><td>100</td></tr></table>'
result = correct_tables(html_input)
# Returns: '<table><caption>Sales</caption>{"Region": {"Q1": "100"}}</table>'

# File processing
with open('input.txt', 'r') as f:
    content = f.read()
result = correct_tables(content)
# Saves to 'all_tables_converted.txt'

# Dictionary input (di_parser format)
dict_input = "{'content': '<table>...</table>', 'language': 'en'}"
result = correct_tables(dict_input)
# Processes nested dictionary structure

# Complex table with suffix columns
complex_html = '<table><tr><th>Name</th><th>Name_2</th><th>Age</th></tr><tr><td>John</td><td>Smith</td><td>25</td></tr></table>'
result = correct_tables(complex_html)
# Returns: '<table>{"Name": "John, Smith", "Age": "25"}</table>'
```

## Configuration and Customization

### Classification Thresholds
- Empty ratio threshold: 40% for alignment-based classification
- Meaningful column threshold: 50% non-empty cells
- Colspan detection: Any cell with colspan > 1

### Symbol Replacement
- Checkbox symbols: ☐ → No, ☑ → Yes
- Cross symbols: ✗ → No, ✓ → Yes
- Context-aware: Depends on surrounding text

### Output Formatting
- Comma separation for suffix columns
- Preserved empty values in JSON
- Single-line output with whitespace removal
