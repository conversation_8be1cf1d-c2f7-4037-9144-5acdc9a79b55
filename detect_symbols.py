#!/usr/bin/env python3
import finalcode2

def check_symbol_detection(table_content: str):
    """
    Check if symbols are being detected in the table content.
    
    Args:
        table_content: The table content to analyze
    """
    print("=== SYMBOL DETECTION TEST ===")
    print()
    
    # Detect all symbols in the content
    symbols_found = finalcode2.detect_unicode_symbols(table_content)
    
    if symbols_found:
        print(f"Found {len(symbols_found)} unique symbols:")
        for symbol_info in symbols_found:
            print(f"  {symbol_info['symbol']} → {symbol_info['unicode_code']} ({symbol_info['category']})")
    else:
        print("❌ No symbols detected in content")
    
    print()
    return symbols_found

def test_symbol_lists():
    """
    Display current symbol lists and test specific checkbox symbols.
    """
    print("=== CURRENT SYMBOL LISTS ===")
    print()
    
    print("POSITIVE_SYMBOLS (→ Yes):")
    for symbol in sorted(finalcode2.POSITIVE_SYMBOLS):
        print(f"  {symbol}")
    
    print("\nNEGATIVE_SYMBOLS (→ No):")
    for symbol in sorted(finalcode2.NEGATIVE_SYMBOLS):
        print(f"  {symbol}")
    
    print("\nNEUTRAL_SYMBOLS (→ Neutral):")
    for symbol in sorted(finalcode2.NEUTRAL_SYMBOLS):
        print(f"  {symbol}")
    
    print(f"\nTotal symbols in lists: {len(finalcode2.ALL_SYMBOLS)}")
    print()

def test_checkbox_symbols(table_content: str):
    """
    Test specific checkbox symbols for recognition and replacement.
    
    Args:
        table_content: The table content to test
    """
    print("=== CHECKBOX SYMBOL TEST ===")
    print()
    
    # Common checkbox symbols to test
    checkbox_symbols = ['☐', '☑', '☒', '□', '■', '▢', '▣', '✓', '✔', '✗', '✘', '❌']
    
    found_checkboxes = []
    for symbol in checkbox_symbols:
        if symbol in table_content:
            found_checkboxes.append(symbol)
            category = finalcode2.get_symbol_category(symbol)
            replacement = finalcode2.get_replacement_for_symbol(symbol)
            in_positive = symbol in finalcode2.POSITIVE_SYMBOLS
            in_negative = symbol in finalcode2.NEGATIVE_SYMBOLS
            in_neutral = symbol in finalcode2.NEUTRAL_SYMBOLS
            
            print(f"{symbol} → category: {category}, replacement: {replacement}")
            print(f"     in_positive: {in_positive}, in_negative: {in_negative}, in_neutral: {in_neutral}")
    
    if not found_checkboxes:
        print("❌ No common checkbox symbols found in content")
    else:
        print(f"\n✅ Found {len(found_checkboxes)} checkbox symbols: {', '.join(found_checkboxes)}")
    
    print()
    return found_checkboxes

def test_processing_pipeline(table_content: str):
    """
    Test the complete processing pipeline and count symbol replacements.
    
    Args:
        table_content: The table content to process
    """
    print("=== PROCESSING PIPELINE TEST ===")
    print()
    
    print("Original content length:", len(table_content))
    
    # Count original symbols
    original_count = sum(table_content.count(s) for s in finalcode2.ALL_SYMBOLS)
    print(f"Original symbols detected: {original_count}")
    
    # Process the content
    try:
        result = finalcode2.correct_tables(table_content)
        print("✅ Processing completed successfully")
        
        # Count remaining symbols
        remaining_count = sum(result.count(s) for s in finalcode2.ALL_SYMBOLS)
        processed_count = original_count - remaining_count
        
        print(f"Remaining symbols: {remaining_count}")
        print(f"Symbols processed: {processed_count}")
        
        # Count replacements
        yes_count = result.count('Yes')
        no_count = result.count('No')
        neutral_count = result.count('Neutral')
        
        print(f"\nReplacement counts:")
        print(f"  Yes: {yes_count}")
        print(f"  No: {no_count}")
        print(f"  Neutral: {neutral_count}")
        print(f"  Total replacements: {yes_count + no_count + neutral_count}")
        
        if processed_count != (yes_count + no_count + neutral_count):
            print("⚠️  WARNING: Symbol count mismatch!")
            print("   Some symbols may have been eliminated instead of replaced")
        
        return result
        
    except Exception as e:
        print(f"❌ Processing failed: {str(e)}")
        return None

def find_missing_symbols(table_content: str):
    """
    Find symbols in content that are not in our current symbol lists.
    
    Args:
        table_content: The table content to analyze
    """
    print("=== MISSING SYMBOLS CHECK ===")
    print()
    
    # Find all unique characters that might be symbols
    potential_symbols = set()
    for char in table_content:
        # Check for Unicode symbols (categories starting with 'S')
        import unicodedata
        try:
            category = unicodedata.category(char)
            if category.startswith('S') or ord(char) > 127:  # Symbol categories or non-ASCII
                potential_symbols.add(char)
        except:
            continue
    
    # Filter out symbols already in our lists
    missing_symbols = potential_symbols - finalcode2.ALL_SYMBOLS
    
    if missing_symbols:
        print(f"Found {len(missing_symbols)} symbols not in current lists:")
        for symbol in sorted(missing_symbols):
            try:
                unicode_name = unicodedata.name(symbol, f"UNKNOWN_{ord(symbol):04X}")
                unicode_code = f"U+{ord(symbol):04X}"
                print(f"  {symbol} → {unicode_code} ({unicode_name})")
            except:
                print(f"  {symbol} → U+{ord(symbol):04X}")
    else:
        print("✅ All symbols in content are already in our lists")
    
    print()
    return missing_symbols

def main():
    """
    Main function to run all debugging tests.
    """
    print("SYMBOL DETECTION AND DEBUGGING TOOL")
    print("=" * 50)
    print()
    
    # Get user input
    print("Please paste your table content below (press Enter twice when done):")
    lines = []
    while True:
        try:
            line = input()
            if line == "" and len(lines) > 0:
                break
            lines.append(line)
        except EOFError:
            break
    
    table_content = "\n".join(lines)
    
    if not table_content.strip():
        print("❌ No content provided. Using test content...")
        table_content = "{'content': '<table><tr><td>☐</td><td>☑</td><td>☒</td></tr></table>', 'language': 'en'}"
    
    print(f"\nAnalyzing content ({len(table_content)} characters)...")
    print()
    
    # Run all tests
    test_symbol_lists()
    symbols_found = check_symbol_detection(table_content)
    checkbox_symbols = test_checkbox_symbols(table_content)
    missing_symbols = find_missing_symbols(table_content)
    result = test_processing_pipeline(table_content)
    
    # Summary
    print("=== SUMMARY ===")
    print()
    if symbols_found:
        print(f"✅ Detected {len(symbols_found)} symbols")
    else:
        print("❌ No symbols detected")
    
    if checkbox_symbols:
        print(f"✅ Found {len(checkbox_symbols)} checkbox symbols")
    else:
        print("❌ No checkbox symbols found")
    
    if missing_symbols:
        print(f"⚠️  Found {len(missing_symbols)} symbols not in current lists")
        print("   Consider adding these symbols to finalcode2.py")
    else:
        print("✅ All symbols are in current lists")
    
    if result:
        print("✅ Processing completed successfully")
    else:
        print("❌ Processing failed")

if __name__ == "__main__":
    main()
