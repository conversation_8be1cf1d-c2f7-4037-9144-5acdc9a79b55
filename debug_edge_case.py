#!/usr/bin/env python3
"""
Debug the edge case issue.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from finalcode2 import process_tables, has_multiple_th_header_rows_with_colspan, build_expanded_matrix

def debug_edge_case():
    """Debug the partial colspan edge case."""
    
    test_html = '''
    <table>
        <tr>
            <th colspan="2">Sales</th>
            <th>2024</th>
        </tr>
        <tr>
            <th>Region</th>
            <th>Q1</th>
            <th>Q2</th>
        </tr>
        <tr>
            <td>North</td>
            <td>100</td>
            <td>120</td>
        </tr>
    </table>
    '''
    
    tables = process_tables(test_html)
    if tables:
        table = tables[0]
        print("Table data:")
        for i, row in enumerate(table['with_span']):
            print(f"Row {i}: {row}")
        
        print("\nExpanded matrix:")
        matrix = build_expanded_matrix(table)
        for i, row in enumerate(matrix):
            print(f"Row {i}: {row}")
        
        print(f"\nTotal columns: {len(matrix[0]) if matrix else 0}")
        
        # Check first row colspan calculation
        first_row = table['with_span'][0]
        first_row_total_colspan = 0
        for cell in first_row:
            if cell.get('tag', '').lower() == 'th':
                colspan = cell.get('colspan', 1)
                print(f"Cell: {cell['text']}, colspan: {colspan}")
                first_row_total_colspan += colspan
        
        print(f"First row total colspan: {first_row_total_colspan}")
        
        matches = has_multiple_th_header_rows_with_colspan(table)
        print(f"Matches pattern: {matches}")

if __name__ == "__main__":
    debug_edge_case()
