#!/usr/bin/env python3
"""
Test script to verify that the new functionality doesn't break existing table processing.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from finalcode2 import correct_tables

def test_comprehensive():
    """Test with comprehensive test output to ensure no regression."""
    
    try:
        with open('comprehensive_test_output.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("Testing comprehensive output...")
        result = correct_tables(content)
        
        # Count tables processed
        table_count = result.count('<table>')
        print(f"Processed {table_count} tables successfully")
        
        # Save result for comparison
        with open('comprehensive_test_result_new.txt', 'w', encoding='utf-8') as f:
            f.write(result)
        
        print("Comprehensive test completed successfully!")
        print("Result saved to comprehensive_test_result_new.txt")
        
        return True
        
    except FileNotFoundError:
        print("comprehensive_test_output.txt not found, skipping comprehensive test")
        return False
    except Exception as e:
        print(f"Error during comprehensive test: {e}")
        return False

if __name__ == "__main__":
    test_comprehensive()
