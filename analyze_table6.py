#!/usr/bin/env python3
import finalcode2
import re

# Analyze Table 6 in detail
with open('di_parser_output.txt', 'r', encoding='utf-8') as f:
    content = f.read()

tables = re.findall(r'<table>.*?</table>', content, re.DOTALL)

print('=== DETAILED ANALYSIS OF TABLE 6 ===')
print()

for table in tables:
    if 'Table 6:' in table:
        print('Raw HTML:')
        print(table[:300] + '...')
        print()
        
        # Process it
        processed_tables = finalcode2.process_tables(table)
        if processed_tables:
            table_data = processed_tables[0]
            
            # Show full matrix
            matrix = finalcode2.build_expanded_matrix(table_data)
            print('Full Matrix:')
            for i, row in enumerate(matrix):
                print(f'  Row {i}: {row}')
            print()
            
            # Analyze structure
            features = finalcode2.extract_table_features(table_data)
            classification = finalcode2.classify_table_from_data(table_data)
            
            print(f'Classification: {classification}')
            print(f'Total rows: {features["total_rows"]}')
            print(f'Header ratio: {features["header_ratio"]:.3f}')
            print()
            
            # Current result
            result = finalcode2.convert_to_key_value_json(table_data, classification)
            print('Current result structure:')
            if isinstance(result, dict):
                for key, value in list(result.items())[:2]:  # Show first 2 items
                    print(f'  {key}: {type(value)} - {str(value)[:100]}...')
            print()
            
            print('PROPOSED: Add table_header preservation for this type')
            break
